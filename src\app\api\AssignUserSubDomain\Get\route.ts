import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const userId = searchParams.get("Id");
        const draw = parseInt(searchParams.get("draw") || "1");

        if (!userId) {
            return NextResponse.json(
                { error: "userId parameter is required" },
                { status: 400 }
            );
        }

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        } else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        // Get sorting parameters and convert to lowercase for case-insensitive comparison
        const orderBy = (searchParams.get("orderBy") || "Id").toLowerCase();
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        // Define allowed sortable fields (must match your Prisma model)
        const allowedOrderFields = [
            'id',
            'subdomainname',
            'subdomainurl',
            'domainname'
        ];

        // Create proper orderBy clause with case-insensitive handling
        let prismaOrderBy = {};
        switch (orderBy) {
            case 'subdomainname':
                prismaOrderBy = {
                    SubDomain: {
                        Name: {
                            sort: orderDir,
                            nulls: 'last' // Handle null values by putting them last
                        }
                    }
                };
                break;
            case 'subdomainurl':
                prismaOrderBy = {
                    SubDomain: {
                        Url: {
                            sort: orderDir,
                            nulls: 'last'
                        }
                    }
                };
                break;
            case 'domainname':
                prismaOrderBy = {
                    SubDomain: {
                        Domain_SubDomain_DomainToDomain: {
                            Name: {
                                sort: orderDir,
                                nulls: 'last'
                            }
                        }
                    }
                };
                break;
            default:
                // Default case for 'createdat'
                prismaOrderBy = {
                    Id: {
                        sort: orderDir,
                        nulls: 'last'
                    }
                };
        }

        // Base filter for mappings
        let where: any = {
            UserId: userId,
            SubDomain: {
                IsDeleted: false,
                Domain_SubDomain_DomainToDomain: {
                    IsDeleted: false
                }
            }
        };

        // Add search conditions if provided (with case-insensitive matching)
        if (search && search !== "") {
            where = {
                ...where,
                SubDomain: {
                    ...where.SubDomain,
                    OR: [
                        { Name: { contains: search, mode: 'insensitive' } },
                        { Url: { contains: search, mode: 'insensitive' } },
                        {
                            Domain_SubDomain_DomainToDomain: {
                                Name: { contains: search, mode: 'insensitive' }
                            }
                        }
                    ]
                }
            };
        }

        // Get total records count (unfiltered)
        const recordsTotal = await prisma.subDomainUserMappings.count({
            where: {
                UserId: userId,
                SubDomain: {
                    IsDeleted: false,
                    Domain_SubDomain_DomainToDomain: {
                        IsDeleted: false
                    }
                }
            }
        });

        // Get filtered records count
        const recordsFiltered = await prisma.subDomainUserMappings.count({
            where
        });

        // Get paginated data with proper sorting
        const mappings = await prisma.subDomainUserMappings.findMany({
            where,
            skip,
            take: limit,
            orderBy: prismaOrderBy,
            include: {
                SubDomain: {
                    include: {
                        Domain_SubDomain_DomainToDomain: {
                            select: {
                                Id: true,
                                Name: true
                            }
                        }
                    }
                }
            }
        });

        // Transform the data with proper null handling
        const transformedData = mappings.map(mapping => ({
            Id: mapping.Id,
            UserId: mapping.UserId,
            SubDomainName: mapping.SubDomain?.Name || null,
            SubDomainUrl: mapping.SubDomain?.Url || null,
            DomainId: mapping.SubDomain?.Domain_SubDomain_DomainToDomain?.Id || null,
            DomainName: mapping.SubDomain?.Domain_SubDomain_DomainToDomain?.Name || null
        }));

        // If for some reason we need client-side sorting (as fallback)
        if (orderBy && allowedOrderFields.includes(orderBy)) {
            transformedData.sort((a, b) => {
                // Get the actual field name from the normalized orderBy
                const field = orderBy === 'id' ? 'Id' :
                    orderBy === 'subdomainname' ? 'SubDomainName' :
                        orderBy === 'subdomainurl' ? 'SubDomainUrl' :
                            'DomainName';

                let aValue = a[field as keyof typeof a];
                let bValue = b[field as keyof typeof b];

                // Handle null/undefined values
                if (aValue == null && bValue == null) return 0;
                if (aValue == null) return orderDir === 'asc' ? 1 : -1;
                if (bValue == null) return orderDir === 'asc' ? -1 : 1;

                // String comparison (case-insensitive)
                const strA = String(aValue).toLowerCase();
                const strB = String(bValue).toLowerCase();
                return orderDir === 'asc'
                    ? strA.localeCompare(strB)
                    : strB.localeCompare(strA);
            });
        }

        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                draw,
                recordsTotal,
                recordsFiltered,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: process.env.NODE_ENV === 'development' ?
                    error instanceof Error ? error.message : String(error)
                    : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}