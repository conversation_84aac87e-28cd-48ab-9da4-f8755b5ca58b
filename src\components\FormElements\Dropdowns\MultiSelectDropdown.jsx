"use client";
import { useEffect, useRef, useState } from "react";
import { GoTriangleUp, GoTriangleDown } from "react-icons/go";

const MultiSelectDropdown = ({
  label,
  options = [],
  placeholder = "Search...",
  value = [],
  onChange,
  error = "",
  required,
  displayKey = "DisplayName",
  idKey = "Id",
  showSelectAll = true,
}) => {
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [justOpened, setJustOpened] = useState(false);

  const filteredOptions = options
    .filter(
      (item) =>
        item[displayKey]?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item[idKey]?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      const aSelected = value.includes(a[idKey]);
      const bSelected = value.includes(b[idKey]);
      if (aSelected && !bSelected) return -1;
      if (!aSelected && bSelected) return 1;
      return 0;
    });

  const allFilteredSelected =
    filteredOptions.length > 0 &&
    filteredOptions.every((item) => value.includes(item[idKey]));

  useEffect(() => {
    if (isDropdownOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isDropdownOpen]);

  const handleToggle = (itemId) => {
    const newValue = value.includes(itemId)
      ? value.filter((id) => id !== itemId)
      : [...value, itemId];
    onChange(newValue);
  };

  const handleSelectAll = () => {
    const filteredIds = filteredOptions.map((item) => item[idKey]);
    if (allFilteredSelected) {
      const newValue = value.filter((id) => !filteredIds.includes(id));
      onChange(newValue);
    } else {
      const newValue = [...new Set([...value, ...filteredIds])];
      onChange(newValue);
    }
  };

  const handleToggleDropdown = () => {
    if (justOpened) return;
    if (isDropdownOpen) {
      setIsDropdownOpen(false);
      setIsFocused(false);
      setSearchTerm("");
    } else {
      setIsDropdownOpen(true);
      setIsFocused(true);
      setJustOpened(true);
      setTimeout(() => setJustOpened(false), 150);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (justOpened) return;
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setIsFocused(false);
        setSearchTerm("");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [justOpened]);

  const hasValue = value.length > 0;

  return (
    <div className="relative z-10 w-full pt-5" ref={dropdownRef}>
      <div
        className={`flex w-full items-center justify-between rounded-lg border-[1.5px] border-stroke bg-transparent px-4 py-4 text-left outline-none transition-all duration-200 focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary ${
          error ? "border-red-500" : ""
        } ${isFocused ? "border-primary" : ""}`}
        onClick={handleToggleDropdown}
        onFocus={() => {
          setIsFocused(true);
          if (!isDropdownOpen) {
            setIsDropdownOpen(true);
            setJustOpened(true);
            setTimeout(() => setJustOpened(false), 150);
          }
        }}
        onBlur={() => {
          setTimeout(() => {
            if (dropdownRef.current && !dropdownRef.current.contains(document.activeElement)) {
              setIsDropdownOpen(false);
              setIsFocused(false);
              setSearchTerm("");
            }
          }, 50);
        }}
        tabIndex={0}
      >
        <span className={`text-dark dark:text-white ${!hasValue ? "opacity-0" : ""}`}>
          {value.length === 0
            ? ""
            : `${value.length} ${value.length === 1 ? "item" : "items"} selected`}
        </span>
        {isDropdownOpen ? <GoTriangleUp /> : <GoTriangleDown />}
      </div>

      <label
        className={`absolute left-4 top-8 cursor-text transition-all duration-200 ${
          isFocused || isDropdownOpen || hasValue
            ? "left-3 top-1 -translate-y-4 bg-white px-1 text-xs dark:bg-dark-2"
            : "text-dark-6 dark:text-white"
        } ${isFocused || isDropdownOpen ? "text-primary" : ""} ${
          error ? "text-red-500" : ""
        }`}
        onClick={handleToggleDropdown}
      >
          {label} {required && <span className="ml-1 select-none text-red">*</span>}
      </label>

      {isDropdownOpen && (
        <div className="absolute mt-1 w-full rounded-lg border border-stroke bg-white shadow-lg dark:border-dark-3 dark:bg-gray-dark z-50">
          <div className="p-2">
            <input
              ref={inputRef}
              type="text"
              className="w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-4 py-4 text-sm outline-none focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Escape") {
                  setIsDropdownOpen(false);
                  setIsFocused(false);
                  setSearchTerm("");
                }
              }}
              required={required}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
          <div className="max-h-60 overflow-y-auto">
            {showSelectAll && filteredOptions.length > 0 && (
              <div
                className="flex cursor-pointer items-center gap-2 px-3 py-2 hover:bg-gray-100 dark:hover:bg-dark-3"
                onClick={handleSelectAll}
              >
                <input
                  type="checkbox"
                  checked={allFilteredSelected}
                  onChange={handleSelectAll}
                  className="rounded border-stroke text-primary focus:ring-primary dark:border-dark-3"
                  onClick={(e) => e.stopPropagation()}
                />
                <span>Select All</span>
              </div>
            )}
            {filteredOptions.length > 0 ? (
              filteredOptions.map((item) => (
                <div
                  key={item[idKey]}
                  className="flex cursor-pointer items-center gap-2 px-3 py-2 hover:bg-gray-100 dark:hover:bg-dark-3"
                  onClick={() => handleToggle(item[idKey])}
                >
                  <input
                    type="checkbox"
                    checked={value.includes(item[idKey])}
                    onChange={() => handleToggle(item[idKey])}
                    className="rounded border-stroke text-primary focus:ring-primary dark:border-dark-3"
                    onClick={(e) => e.stopPropagation()}
                  />
                  <span>{item[displayKey]}</span>
                </div>
              ))
            ) : (
              <div className="px-3 py-3 text-sm text-gray-500 dark:text-gray-400">
                No options found
              </div>
            )}
          </div>
        </div>
      )}
      {error && (
        <p className="absolute -bottom-5 mt-1 text-xs text-red-500">{error}</p>
      )}
    </div>
  );
};

export default MultiSelectDropdown;