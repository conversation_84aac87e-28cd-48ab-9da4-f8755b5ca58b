"use client";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
} from "@mui/material";
import axios from "axios";
import Swal from "sweetalert2";
import { IoCloseOutline } from "react-icons/io5";

import InputGroup from "@/components/FormElements/InputGroup";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { Button } from "@/components/ui-elements/button";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import { decodeJWT } from "@/utils/functions";

export default function AdminSettings() {
  const [settings, setSettings] = useState({
    LmStyleId: "",
    DmStyleId: "",
    ChannalId: "",
    PubId: "",
    AdsAccountId: "",
    AdsClientId: "",
    // HeadTagJSON: "{}",
  });

  // Separate state for Ads credentials
  const [adsCredentials, setAdsCredentials] = useState({
    client_id: "",
    client_secret: "",
    redirect_uri: process.env.NEXT_PUBLIC_REDIRECT_URL_ADS || "",
    scope: process.env.NEXT_PUBLIC_SCOPE_ADS || "",
  });

  // Separate state for AdSense credentials
  const [adSenseCredentials, setAdSenseCredentials] = useState({
    client_id: "",
    client_secret: "",
    redirect_uri: process.env.NEXT_PUBLIC_REDIRECT_URL_ADSENSE || "",
    scope: process.env.NEXT_PUBLIC_SCOPE_ADSENSE || "",
  });

  const [channels, setChannels] = useState({
    channelsList: [],
    isLoading: false,
  });
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
  });
  const [passwordErrors, setPasswordErrors] = useState({
    newPassword: "",
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [profile, setProfile] = useState<any>();
  const [isAuthLoading, setIsAuthLoading] = useState({
    ads: false,
    adsense: false,
  });

  interface ChannelItem {
    Id: string;
    DisplayName: string;
  }

  useEffect(() => {
    const token: any = localStorage.getItem("accessToken");
    const user = decodeJWT(token);
    setProfile(user);
  }, []);

  const isSuperAdmin = profile?.User_Type === "Super Admin";

  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/AdminSetting/Get", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        });
        const data = await response.json();
        if (data.success && data.data) {
          setSettings({
            LmStyleId: data.data.LmStyleId || "",
            DmStyleId: data.data.DmStyleId || "",
            ChannalId: data.data.ChannalId || "",
            PubId: data.data.PubId || "",
            AdsAccountId: data.data.AdsAccountId || "",
            AdsClientId: data.data.AdsClientId || "",
            // HeadTagJSON: data.data.HeadTagJSON || "{}",
          });

          // Parse and set Ads credentials
          if (data.data.AdsCredentialsJSON) {
            try {
              const parsedAdsCredentials = JSON.parse(
                data.data.AdsCredentialsJSON,
              );
              setAdsCredentials({
                client_id: parsedAdsCredentials.client_id || "",
                client_secret: parsedAdsCredentials.client_secret || "",
                redirect_uri:
                  parsedAdsCredentials.redirect_uri ||
                  process.env.NEXT_PUBLIC_REDIRECT_URL_ADS ||
                  "",
                scope:
                  parsedAdsCredentials.scope ||
                  process.env.NEXT_PUBLIC_SCOPE_ADS ||
                  "",
              });
            } catch (error) {
              console.error("Error parsing AdsCredentialsJSON:", error);
            }
          } else {
            // Fallback to legacy fields
            setAdsCredentials({
              client_id: data.data.CampaignClientId || "",
              client_secret: data.data.CampaignClientSecret || "",
              redirect_uri: process.env.NEXT_PUBLIC_REDIRECT_URL_ADS || "",
              scope: process.env.NEXT_PUBLIC_SCOPE_ADS || "",
            });
          }

          // Parse and set AdSense credentials
          if (data.data.AdSenseCredentialsJSON) {
            try {
              const parsedAdSenseCredentials = JSON.parse(
                data.data.AdSenseCredentialsJSON,
              );
              setAdSenseCredentials({
                client_id: parsedAdSenseCredentials.client_id || "",
                client_secret: parsedAdSenseCredentials.client_secret || "",
                redirect_uri:
                  parsedAdSenseCredentials.redirect_uri ||
                  process.env.NEXT_PUBLIC_REDIRECT_URL_ADSENSE ||
                  "",
                scope:
                  parsedAdSenseCredentials.scope ||
                  process.env.NEXT_PUBLIC_SCOPE_ADSENSE ||
                  "",
              });
            } catch (error) {
              console.error("Error parsing AdSenseCredentialsJSON:", error);
            }
          } else {
            // Fallback to legacy fields
            setAdSenseCredentials({
              client_id: data.data.RevenueClientId || "",
              client_secret: data.data.RevenueClientSecret || "",
              redirect_uri: process.env.NEXT_PUBLIC_REDIRECT_URL_ADSENSE || "",
              scope: process.env.NEXT_PUBLIC_SCOPE_ADSENSE || "",
            });
          }
        } else {
          Swal.fire({
            icon: "error",
            title: "Error",
            text: data.error || "Failed to load settings",
          });
        }
      } catch (error) {
        Swal.fire({
          icon: "error",
          title: "Error",
          text: "Failed to load settings",
        });
      } finally {
        setIsLoading(false);
      }
    };

    const fetchChannels = async () => {
      try {
        setChannels((prev) => ({ ...prev, isLoading: true }));
        const response = await axios.get("/api/Channals/GetDropdown", {
          withCredentials: true,
        });
        if (response.data.success) {
          setChannels({
            channelsList: response.data.data,
            isLoading: false,
          });
        } else {
          console.error("Failed to fetch channels:", response.data.error);
          setChannels((prev) => ({ ...prev, isLoading: false }));
        }
      } catch (error) {
        console.error("Error fetching channels:", error);
        setChannels((prev) => ({ ...prev, isLoading: false }));
      }
    };

    fetchSettings();
    fetchChannels();
  }, []);

  // Handle OAuth initiation
  const initiateOAuth = (type: "adwords" | "adsense") => {
    setIsAuthLoading((prev) => ({ ...prev, [type]: true }));
    try {
      const creds = type === "adwords" ? adsCredentials : adSenseCredentials;

      if (
        !creds.client_id ||
        !creds.client_secret ||
        !creds.redirect_uri ||
        !creds.scope
      ) {
        throw new Error("Missing required credentials fields");
      }

      const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${encodeURIComponent(
        creds.client_id,
      )}&redirect_uri=${encodeURIComponent(
        creds.redirect_uri,
      )}&response_type=code&scope=${encodeURIComponent(creds.scope)}&access_type=offline&prompt=consent`;
      window.open(authUrl, "_blank");
    } catch (error: any) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: `Failed to initiate ${type === "adwords" ? "AdWords" : "AdSense"} authentication: ${error.message}`,
      });
    } finally {
      setIsAuthLoading((prev) => ({ ...prev, [type]: false }));
    }
  };

  const handleSettingsChange = (
    e:
      | React.ChangeEvent<
          HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
        >
      | { target: { name: string; value: string } },
  ) => {
    const { name, value } = e.target;
    setSettings((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAdsCredentialsChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { name, value } = e.target;
    setAdsCredentials((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAdSenseCredentialsChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { name, value } = e.target;
    setAdSenseCredentials((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSettingsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      // Prepare settings data with JSON credentials
      const settingsData = {
        ...settings,
        AdsCredentialsJSON: JSON.stringify(adsCredentials, null, 2),
        AdSenseCredentialsJSON: JSON.stringify(adSenseCredentials, null, 2),
      };

      const response = await fetch("/api/AdminSetting/Get", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(settingsData),
      });
      const data = await response.json();
      if (data.success) {
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: data.message || "Settings updated successfully",
          timer: 2000,
          showConfirmButton: false,
        });
      } else {
        throw new Error(data.error || "Failed to update settings");
      }
    } catch (error: any) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({ ...prev, [name]: value }));
  
    if (name === "newPassword") {
      setPasswordErrors((prev) => ({
        ...prev,
        newPassword: "",
      }));
    }
  };
  

  const validatePasswordForm = () => {
    let isValid = true;
    const newErrors = {
      newPassword: "",
    };
  
    if (!passwordData.oldPassword) {
      setIsModalOpen(false);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Current password is required",
      });
      return false;
    }
  
    if (!passwordData.newPassword) {
      newErrors.newPassword = "New password is required";
      isValid = false;
    } else if (passwordData.newPassword.length < 8) {
      newErrors.newPassword = "Password must be at least 8 characters long";
      isValid = false;
    } else if (passwordData.oldPassword === passwordData.newPassword) {
      newErrors.newPassword = "New password must be different from current password";
      isValid = false;
    }
  
    setPasswordErrors(newErrors);
  
    return isValid;
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
  
    if (!validatePasswordForm()) {
      return;
    }
  
    setIsLoading(true);
    try {
      const requestData = {
        oldPassword: passwordData.oldPassword,
        newPassword: passwordData.newPassword,
      };
  
      const response = await fetch("/api/adminuser/ResetPassword", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestData),
      });
      
      const data = await response.json();
      
      if (data.success) {
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: data.message || "Password updated successfully",
          timer: 2000,
          showConfirmButton: false,
        });
        setIsModalOpen(false);
        setPasswordData({
          oldPassword: "",
          newPassword: "",
        });
        setPasswordErrors({ newPassword: "" });
      } else {
        throw new Error(data.error || "Failed to update password");
      }
    } catch (error: any) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to update password",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
      <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-white">Settings Management</h1>
        </div>
      </div>
      <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-[10px] border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="mb-1 text-2xl font-bold text-dark dark:text-white">
            Settings
          </h2>
        </div>
        {isSuperAdmin && (
          <form
            className="mx-auto grid w-full grid-cols-1 gap-6 md:grid-cols-2"
            onSubmit={handleSettingsSubmit}
          >
            <InputGroup
              label="LM Style ID"
              type="text"
              name="LmStyleId"
              value={settings.LmStyleId}
              handleChange={handleSettingsChange}
              placeholder="Enter LMS Style ID"
              disabled={isLoading}
            />

            <InputGroup
              label="DM Style ID"
              type="text"
              name="DmStyleId"
              value={settings.DmStyleId}
              handleChange={handleSettingsChange}
              placeholder="Enter DM Style ID"
              disabled={isLoading}
            />

            <SearchableDropdown
              label="Channel ID"
              options={channels.channelsList}
              placeholder="Select Channel"
              value={settings.ChannalId}
              onChange={(item: ChannelItem) =>
                handleSettingsChange({
                  target: { name: "ChannalId", value: item?.Id },
                })
              }
              required={false}
              displayKey="DisplayName"
              idKey="Id"
            />

            <InputGroup
              label="Pub ID"
              type="text"
              name="PubId"
              value={settings.PubId}
              handleChange={handleSettingsChange}
              placeholder="Enter Pub ID"
              disabled={isLoading}
            />

            <InputGroup
              label="Ads Account ID"
              type="text"
              name="AdsAccountId"
              value={settings.AdsAccountId}
              handleChange={handleSettingsChange}
              placeholder="Enter Ads Account ID"
              disabled={isLoading}
            />

            <InputGroup
              label="Ads Client ID"
              type="text"
              name="AdsClientId"
              value={settings.AdsClientId}
              handleChange={handleSettingsChange}
              placeholder="Enter Ads Client ID"
              disabled={isLoading}
            />

            {/* <TextAreaGroup
              label="Head Tag JSON"
              name="HeadTagJSON"
              value={settings.HeadTagJSON}
              handleChange={handleSettingsChange}
              rows={4}
              className="md:col-span-2"
              disabled={isLoading}
            /> */}

            {/* Ads Credentials Section */}
            <div className="md:col-span-1">
              <h3 className="mb-4 text-lg font-semibold text-dark dark:text-white">
                Ads Credentials
              </h3>
              <div className="space-y-4">
                <InputGroup
                  label="Client ID"
                  type="text"
                  name="client_id"
                  value={adsCredentials.client_id}
                  handleChange={handleAdsCredentialsChange}
                  placeholder="Enter Ads Client ID"
                  disabled={isLoading}
                />
                <InputGroup
                  label="Client Secret"
                  type="text"
                  name="client_secret"
                  value={adsCredentials.client_secret}
                  handleChange={handleAdsCredentialsChange}
                  placeholder="Enter Ads Client Secret"
                  disabled={isLoading}
                />
                <InputGroup
                  label="Redirect URI"
                  type="text"
                  name="redirect_uri"
                  value={adsCredentials.redirect_uri}
                  handleChange={handleAdsCredentialsChange}
                  placeholder="Enter Ads Redirect URI"
                  disabled={isLoading}
                />
                <InputGroup
                  label="Scope"
                  type="text"
                  name="scope"
                  value={adsCredentials.scope}
                  handleChange={handleAdsCredentialsChange}
                  placeholder="Enter Ads Scope"
                  disabled={isLoading}
                />
                <Button
                  type="button"
                  label={isAuthLoading.ads ? "Processing..." : "Login Ads"}
                  variant="primary"
                  shape="rounded"
                  onClick={() => initiateOAuth("adwords")}
                  disabled={isLoading || isAuthLoading.ads}
                  className="mt-2 w-full"
                />
              </div>
            </div>

            {/* AdSense Credentials Section */}
            <div className="md:col-span-1">
              <h3 className="mb-4 text-lg font-semibold text-dark dark:text-white">
                AdSense Credentials
              </h3>
              <div className="space-y-4">
                <InputGroup
                  label="Client ID"
                  type="text"
                  name="client_id"
                  value={adSenseCredentials.client_id}
                  handleChange={handleAdSenseCredentialsChange}
                  placeholder="Enter AdSense Client ID"
                  disabled={isLoading}
                />
                <InputGroup
                  label="Client Secret"
                  type="text"
                  name="client_secret"
                  value={adSenseCredentials.client_secret}
                  handleChange={handleAdSenseCredentialsChange}
                  placeholder="Enter AdSense Client Secret"
                  disabled={isLoading}
                />
                <InputGroup
                  label="Redirect URI"
                  type="text"
                  name="redirect_uri"
                  value={adSenseCredentials.redirect_uri}
                  handleChange={handleAdSenseCredentialsChange}
                  placeholder="Enter AdSense Redirect URI"
                  disabled={isLoading}
                />
                <InputGroup
                  label="Scope"
                  type="text"
                  name="scope"
                  value={adSenseCredentials.scope}
                  handleChange={handleAdSenseCredentialsChange}
                  placeholder="Enter AdSense Scope"
                  disabled={isLoading}
                />
                <Button
                  type="button"
                  label={
                    isAuthLoading.adsense ? "Processing..." : "Login AdSense"
                  }
                  variant="primary"
                  shape="rounded"
                  onClick={() => initiateOAuth("adsense")}
                  disabled={isLoading || isAuthLoading.adsense}
                  className="mt-2 w-full"
                />
              </div>
            </div>

            <div className="flex gap-4 pt-4 sm:flex-row md:col-span-2">
              <Button
                type="submit"
                label={isLoading ? "Processing..." : "Submit"}
                variant="primary"
                shape="rounded"
                className="flex items-center justify-center gap-2"
                disabled={isLoading}
              />
              <Button
                type="reset"
                label="Reset Password"
                variant="dark"
                shape="rounded"
                className="flex items-center justify-center gap-2"
                disabled={isLoading}
                onClick={(e) => {
                  e.preventDefault();
                  setIsModalOpen(true);
                  setPasswordData({
                    oldPassword: "",
                    newPassword: "",
                  });
                }}
              />
            </div>
          </form>
        )}

        {!isSuperAdmin && (
          <div className="flex justify-center">
            <Button
              type="reset"
              label="Reset Password"
              variant="dark"
              shape="rounded"
              className="flex items-center justify-center gap-2"
              disabled={isLoading}
              onClick={(e) => {
                e.preventDefault();
                setIsModalOpen(true);
                setPasswordData({
                  oldPassword: "",
                  newPassword: ""
                });
              }}
            />
          </div>
        )}

        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          fullWidth
          maxWidth="sm"
          PaperProps={{
            sx: {
              maxHeight: "90vh",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              color: "white",
              py: 2,
              px: 3,
            }}
            className="bg-primary text-white"
          >
            <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
              Reset Password
            </span>
            <IconButton
              aria-label="close"
              onClick={() => setIsModalOpen(false)}
              sx={{
                color: "white",
              }}
            >
              <IoCloseOutline size={24} />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers sx={{ py: 3, px: 3 }}>
            <form className="space-y-4">
              <InputGroup
                label="Current Password"
                type="password"
                name="oldPassword"
                value={passwordData.oldPassword}
                handleChange={handlePasswordChange}
                placeholder="Enter current password"
                disabled={isLoading}
              />
              <div>
                <InputGroup
                  label="New Password"
                  type="password"
                  name="newPassword"
                  value={passwordData.newPassword}
                  handleChange={handlePasswordChange}
                  placeholder="Enter new password (min 8 characters)"
                  disabled={isLoading}
                />
                {passwordErrors.newPassword && (
                  <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                    {passwordErrors.newPassword}
                  </Typography>
                )}
              </div>
            </form>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <Button
              type="submit"
              label={isLoading ? "Processing..." : "Reset Password"}
              variant="primary"
              shape="rounded"
              onClick={handlePasswordSubmit}
              disabled={isLoading}
            />
          </DialogActions>
        </Dialog>
      </div>
    </div>
  );
}
