import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            User_Type: string;
            Id: string;
        };
        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        let where: any = {
            IsDeleted: false
        };

        if (user.User_Type !== 'Super Admin' && user.User_Type !== 'Admin') {
            where.User_Id_Settings = user.Id;
        }
        const articleWithUsers = await prisma.articleDetails.findMany({
            where,
            orderBy: {
                CreatedAt: 'desc'
            },
            select: {
                Id: true,
                Title: true,
            }
        });

        const transformedData = articleWithUsers.map(articleDetails => ({
            ...articleDetails,
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
