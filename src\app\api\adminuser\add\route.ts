import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcrypt';
import { verifyToken } from '../../../../lib/varifyToken';
import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";
const SALT_ROUNDS = 10;
const DEFAULT_ACCESS_DURATION_YEARS = 1;

export async function POST(req: NextRequest) {
    try {
        // Authentication
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Parse form data
        const formData = await req.formData();

        // Extract text fields
        const Name = formData.get('Name') as string | null;
        const Email = formData.get('Email') as string | null;
        const Password = formData.get('Password') as string | null;
        const User_Type = formData.get('User_Type') as string | null;
        const Number = formData.get('Number') as string | null;
        const Status = formData.get('Status') as string | null;
        const AccessExpiration = formData.get('AccessExpiration') as string | null;
        const AboutMe = formData.get('AboutMe') as string | null;

        // Validate required fields
        if (!Name || !Email || !User_Type || !Number || !Password) {
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            );
        }

        // Check for existing user
        const emailExists = await prisma.adminUser.findUnique({
            where: {
                Email,
                IsDeleted: false
            }
        });

        if (emailExists) {
            return NextResponse.json(
                { error: "Email already registered" },
                { status: 409 }
            );
        }

        // Handle file upload
        const file = formData.get('ProfilePic') as File | null;
        let imagePath: string = "ProfilePic.png"; // default value

        if (file && file.size > 0) {
            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                const mimeType = file.type;
                const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
                const uniqueId = uuidv4();

                if (!fs.existsSync(UPLOAD_DIR)) {
                    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
                }

                // Process image versions in parallel
                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                // Save all versions
                const baseFileName = `${uniqueId}.${originalExtension}`;
                const versions = [
                    { suffix: '', buffer: originalBuffer },
                    { suffix: '_small', buffer: smallBuffer },
                    { suffix: '_medium', buffer: mediumBuffer }
                ];

                await Promise.all(versions.map(({ suffix, buffer }) => {
                    const fileName = `${uniqueId}${suffix}.${originalExtension}`;
                    const filePath = path.resolve(UPLOAD_DIR, fileName);
                    return fs.promises.writeFile(filePath, buffer);
                }));

                imagePath = baseFileName;
            } catch (error: unknown) {
                console.error("Image processing error:", error);
                // Don't fail the entire request if image processing fails
                // Just continue with the default image
            }
        }

        // Prepare expiration date (1 year from now if not provided)
        const expirationDate = AccessExpiration
            ? new Date(AccessExpiration)
            : new Date(new Date().setFullYear(new Date().getFullYear() + DEFAULT_ACCESS_DURATION_YEARS));

        // Hash password
        const hashedPassword = await bcrypt.hash(Password, SALT_ROUNDS);

        // Create user
        const newUser = await prisma.adminUser.create({
            data: {
                Name,
                Email,
                Password: hashedPassword,
                User_Type,
                Number,
                Status: Status ? Status === 'true' : false,
                ProfilePic: imagePath,
                AccessExpiration: expirationDate,
                AboutMe
            },
            select: {
                Id: true,
                Name: true,
                Email: true,
                User_Type: true,
                Number: true,
                Status: true,
                ProfilePic: true,
                AccessExpiration: true,
                AboutMe: true
            }
        });

        return NextResponse.json(
            {
                success: true,
                message: "User created successfully",
                data: {
                    ...newUser,
                    accessDuration: DEFAULT_ACCESS_DURATION_YEARS + " year(s)"
                }
            },
            { status: 201 }
        );

    } catch (error) {
        console.error("User creation failed:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                ...(process.env.NODE_ENV === 'development' && {
                    details: error instanceof Error ? error.message : String(error)
                })
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}