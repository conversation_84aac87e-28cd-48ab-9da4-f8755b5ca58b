import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

interface RevenueCampaignData {
    articleid?: string;
    articlename?: string;
    date: Date;
    rv_revenue: number;
    rv_rpc: number;
    profit: number;
    roi: number;
    ads_conversions: number;
    ads_click: number;
    ads_spend: number;
    ads_cpa: number;
    ads_cpc: number;
    channelid?: string;
    channelname?: string;
    country?: string;
}

export async function POST(req: NextRequest) {
    try {
        // Verify user authentication
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get parameters from request body
        const body = await req.json();
        const { articleId, channelId, groupBy, orderBy = 'ads_spend', orderDir = 'desc' } = body;

        // Get parameters from URL search params
        const { searchParams } = new URL(req.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const showNoRevenue = searchParams.get('showNoRevenue') === 'true';

        // Validate required dates
        if (!startDate || !endDate) {
            return NextResponse.json({
                error: 'Both startDate and endDate are required'
            }, { status: 400 });
        }

        // Validate sort parameters
        const validSortFields = [
            'ads_spend', 'rv_revenue', 'profit', 'roi',
            'ads_conversions', 'ads_click', 'ads_cpa', 'ads_cpc',
            'date', 'articlename', 'channelname', 'country', 'rv_rpc'
        ];

        if (!validSortFields.includes(orderBy.toLowerCase())) {
            return NextResponse.json({
                error: `Invalid orderBy parameter. Valid options: ${validSortFields.join(', ')}`
            }, { status: 400 });
        }

        if (!['asc', 'desc'].includes(orderDir.toLowerCase())) {
            return NextResponse.json({
                error: 'Invalid orderDir parameter. Use asc or desc'
            }, { status: 400 });
        }

        // Process parameters with proper null handling
        const processedArticleId = articleId ?
            (Array.isArray(articleId) ? articleId.join(',') : articleId.toString()) :
            null;

        const processedChannelId = channelId ?
            (Array.isArray(channelId) ? channelId.join(',') : channelId.toString()) :
            null;

        const processedGroupBy = groupBy || 'article';

        // Call the database function to populate temp table
        await prisma.$executeRawUnsafe(
            `SELECT * FROM fn_ads_revenue_data_test(
                $1::text, 
                $2::text, 
                $3::date, 
                $4::date, 
                $5::text, 
                $6::boolean
            )`,
            processedArticleId,
            processedChannelId,
            new Date(startDate),
            new Date(endDate),
            processedGroupBy,
            showNoRevenue
        );

        // Build dynamic SELECT query based on groupBy parameter
        const groupByFields = processedGroupBy.toLowerCase().split(',').map((field: string) => field.trim());

        // Start with core metrics that should always be present
        // Note: rv_rpc is calculated as rv_revenue/ads_click
        let selectFields = `
            "date"::timestamp as "date",
            "rv_revenue"::float as "rv_revenue",
            CASE 
                WHEN "ads_click" > 0 THEN ("rv_revenue"::float / "ads_click"::float)
                ELSE 0 
            END as "rv_rpc",
            "profit"::float as "profit",
            "roi"::float as "roi",
            "ads_conversions"::integer as "ads_conversions",
            "ads_click"::integer as "ads_click",
            "ads_spend"::float as "ads_spend",
            "ads_cpa"::float as "ads_cpa",
            "ads_cpc"::float as "ads_cpc"
        `;

        // Add article fields only if groupBy includes article
        if (groupByFields.includes('article')) {
            selectFields = `
            "articleid"::text as "articleid",
            "article_name"::text as "articlename",` + selectFields;
        }

        // Add channel fields if groupBy includes channel
        if (groupByFields.includes('channel')) {
            selectFields += `,
            "channelid"::text as "channelid",
            "channel_name"::text as "channelname"`;
        }

        // Add country fields if groupBy includes country
        if (groupByFields.includes('country')) {
            selectFields += `,
            "country"::text as "country"`;
        }

        // Build ORDER BY clause dynamically with default sorting by ads_spend DESC
        let orderByClause = `"${orderBy}" ${orderDir}`;

        // Add secondary sorting for better consistency
        if (orderBy !== 'date') {
            orderByClause += ', "date" DESC';
        }
        if (groupByFields.includes('article') && orderBy !== 'articlename') {
            orderByClause += ', "article_name"';
        }
        if (groupByFields.includes('channel') && orderBy !== 'channelname') {
            orderByClause += ', "channel_name"';
        }
        if (groupByFields.includes('country') && orderBy !== 'country') {
            orderByClause += ', "country"';
        }

        // Fetch results from temp table with dynamic fields
        const rawData = await prisma.$queryRawUnsafe<RevenueCampaignData[]>(`
            SELECT ${selectFields}
            FROM pg_temp.temp_ads_result
            ORDER BY ${orderByClause}
        `);

        // Return successful response with data and filter information
        return NextResponse.json({
            success: true,
            data: rawData,
            sort: {
                by: orderBy,
                direction: orderDir
            }
        });

    } catch (error) {
        console.error('Error fetching revenue campaign data:', error);

        // Handle specific Prisma errors
        if (error instanceof Error) {
            if (error.message.includes('42703')) {
                return NextResponse.json(
                    { error: 'Database column does not exist. Please check the database function.' },
                    { status: 500 }
                );
            }
            if (error.message.includes('42P01')) {
                return NextResponse.json(
                    { error: 'Database table does not exist. Please check the database function.' },
                    { status: 500 }
                );
            }
        }

        return NextResponse.json(
            {
                error: 'Failed to fetch revenue campaign data',
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    } finally {
        // Clean up database connection
        await prisma.$disconnect();
    }
}