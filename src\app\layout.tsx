import "@/css/satoshi.css";
import "@/css/style.css";
import "flatpickr/dist/flatpickr.min.css";
import "jsvectormap/dist/jsvectormap.css";
import "react-toastify/dist/ReactToastify.css";

import type { Metadata } from "next";
// import NextTopLoader from "nextjs-toploader";
import { ToastContainer } from "react-toastify";
import MuiThemeProvider from "@/lib/theme-provider"; 
import type { PropsWithChildren } from "react";
import { Providers } from "./providers";
import { LayoutClientWrapper } from "./LayoutClientwrapper";

export const metadata: Metadata = {
  title: {
    default: "Smart Adpilot",
    template: "%s | NextBlog - Next.js Blogging Platform",
  },
  description:
    "Next.js blogging platform with customizable themes, SEO optimizations, and easy content management.",
};


export default function RootLayout({ children }: PropsWithChildren) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <Providers>
           <MuiThemeProvider>
           <ToastContainer />
          {/* <NextTopLoader color="#5750F1" showSpinner={false} /> */}
          <LayoutClientWrapper>
            {children}
          </LayoutClientWrapper>
            </MuiThemeProvider>
        </Providers>
      </body>
    </html>
  );
}

