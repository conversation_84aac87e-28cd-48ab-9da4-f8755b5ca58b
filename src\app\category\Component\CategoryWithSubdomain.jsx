"use client";
import React, { useCallback, useEffect, useState } from "react";
import axios from "axios";
import Swal from "sweetalert2";
import useDebounce from "@/hooks/useDebounce";
import { IoCloseOutline } from "react-icons/io5";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import { FaPlus } from "react-icons/fa";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Tabs,
  Tab,
  Box,
} from "@mui/material";
import Checkbox from "@/components/FormElements/checkbox";

const CategoryWithSubdomain = () => {
  const [showLoader, setShowLoader] = useState(false);
  const [categories, setCategories] = useState([]);
  const [subdomains, setSubdomains] = useState([]);
  const [domains, setDomains] = useState([]);
  const [selectedDomain, setSelectedDomain] = useState("");
  const [selectedSubdomain, setSelectedSubdomain] = useState("");
  const [tabValue, setTabValue] = useState(0);
  
  const [formData, setFormData] = useState({
    name: "",
    title: "",
    shortDescription: "",
    showUrlName: "",
    image: null,
    assignedSubdomains: [],
  });
  
  const [errors, setErrors] = useState({});
  const [modalState, setModalState] = useState({
    open: false,
    mode: "add",
    id: null,
  });
  
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("Name");
  const [isEditFormInitialized, setIsEditFormInitialized] = useState(false);
  const [editUrlMode, setEditUrlMode] = useState(false);
  const [isUrlManuallyModified, setIsUrlManuallyModified] = useState(false);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const columns = [
    {
      id: "Name",
      label: "Category Name",
    },
    {
      id: "ShowUrlName",
      label: "URL Name",
    },
    {
      id: "SubdomainCount",
      label: "Assigned Subdomains",
    },
  ];

  // Fetch domains for dropdown
  const fetchDomains = useCallback(async () => {
    try {
      const response = await axios.get("/api/Domain/GetDropDown", {
        withCredentials: true,
      });
      if (response.data.success) {
        setDomains(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching domains:", error);
    }
  }, []);

  // Fetch subdomains based on selected domain
  const fetchSubdomains = useCallback(async () => {
    if (!selectedDomain) {
      setSubdomains([]);
      return;
    }
    
    try {
      const response = await axios.get("/api/SubDomain/GetDropDown", {
        params: { DomainId: selectedDomain },
        withCredentials: true,
      });
      if (response.data.success) {
        setSubdomains(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching subdomains:", error);
    }
  }, [selectedDomain]);

  // Fetch categories with subdomain context
  const fetchCategories = useCallback(async () => {
    try {
      setShowLoader(true);
      const params = {
        page: page + 1,
        length: rowsPerPage,
        q: debouncedSearchTerm,
        orderBy,
        orderDir: order,
        _: new Date().getTime(),
      };

      // Add subdomain filter if selected
      if (selectedSubdomain) {
        params.subdomainId = selectedSubdomain;
      }

      const response = await axios.get("/api/category/getAllCategory", {
        params,
        withCredentials: true,
      });

      if (response.status === 200) {
        // For now, we'll use the existing API and simulate subdomain count
        // In a real implementation, this would come from the API
        const categoriesWithSubdomainCount = response.data.data.map(category => ({
          ...category,
          SubdomainCount: Math.floor(Math.random() * 5) + 1, // Simulated count
        }));
        
        setCategories(categoriesWithSubdomainCount);
        setTotalCount(
          response.data.pagination?.recordsFiltered ||
            response.data.data?.length ||
            0,
        );
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch categories",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  }, [page, rowsPerPage, debouncedSearchTerm, orderBy, order, selectedSubdomain]);

  // Generate slug function
  const generateSlug = (text) => {
    return text
      .replace(/[^\p{L}\p{N}\s-]/gu, '')
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  };

  useEffect(() => {
    fetchDomains();
  }, [fetchDomains]);

  useEffect(() => {
    fetchSubdomains();
  }, [fetchSubdomains]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleDomainChange = (domain) => {
    setSelectedDomain(domain?.Id || "");
    setSelectedSubdomain(""); // Reset subdomain when domain changes
    setPage(0);
  };

  const handleSubdomainChange = (subdomain) => {
    setSelectedSubdomain(subdomain?.Id || "");
    setPage(0);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleInputChange = (e) => {
    const { name, value, type, files } = e.target;
    
    if (type === "file") {
      setFormData(prev => ({ ...prev, [name]: files[0] }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
      
      // Auto-generate URL name from category name if not manually modified
      if (name === "name" && !isUrlManuallyModified) {
        const slug = generateSlug(value);
        setFormData(prev => ({ ...prev, showUrlName: slug }));
      }
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const handleUrlChange = (e) => {
    const { value } = e.target;
    setIsUrlManuallyModified(true);
    setFormData(prev => ({ ...prev, showUrlName: generateSlug(value) }));
    
    if (errors.showUrlName) {
      setErrors(prev => ({ ...prev, showUrlName: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Category name is required";
    }
    
    if (!formData.showUrlName.trim()) {
      newErrors.showUrlName = "URL name is required";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      name: "",
      title: "",
      shortDescription: "",
      showUrlName: "",
      image: null,
      assignedSubdomains: [],
    });
    setErrors({});
    setIsEditFormInitialized(false);
    setEditUrlMode(false);
    setIsUrlManuallyModified(false);
  };

  const handleOpenAddModal = () => {
    resetForm();
    setModalState({
      open: true,
      mode: "add",
      id: null,
    });
  };

  const handleCloseModal = () => {
    setModalState({
      open: false,
      mode: "add",
      id: null,
    });
    resetForm();
  };

  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl text-white">
              Category Management with Subdomain Mapping
            </h1>
          </div>
        </div>
        
        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
          
          {/* Filter Section */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-end sm:justify-between">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
              <div className="w-full sm:w-64">
                <SearchableDropdown
                  label="Select Domain"
                  options={domains}
                  placeholder="Select a Domain"
                  value={selectedDomain}
                  onChange={handleDomainChange}
                  displayKey="Name"
                  idKey="Id"
                />
              </div>
              
              {selectedDomain && (
                <div className="w-full sm:w-64">
                  <SearchableDropdown
                    label="Filter by Subdomain (Optional)"
                    options={subdomains}
                    placeholder="All Subdomains"
                    value={selectedSubdomain}
                    onChange={handleSubdomainChange}
                    displayKey="Name"
                    idKey="Id"
                    allowClear
                  />
                </div>
              )}
              
              <div className="w-full sm:w-64">
                <InputGroup
                  placeholder="Search categories..."
                  label="Search"
                  value={searchTerm}
                  handleChange={(e) => setSearchTerm(e.target.value)}
                  type="text"
                />
              </div>
            </div>
            
            <div className="w-full sm:w-auto">
              <Button
                type="button"
                label="Add Category"
                variant="primary"
                shape="rounded"
                icon={<FaPlus size={14} />}
                className="flex w-full items-center justify-center gap-2 sm:w-auto"
                onClick={handleOpenAddModal}
              />
            </div>
          </div>

          {/* Data Table */}
          <CustomDataTable
            isLoading={showLoader}
            columns={columns}
            rows={categories}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={setPage}
            onRowsPerPageChange={(newRows) => {
              setRowsPerPage(newRows);
              setPage(0);
            }}
            totalCount={totalCount}
            order={order}
            orderBy={orderBy}
            onRequestSort={(event, property) => {
              const isAsc = orderBy === property && order === "asc";
              setOrder(isAsc ? "desc" : "asc");
              setOrderBy(property);
            }}
            // onView={handleViewCategory}
            // onEdit={handleOpenEditModal}
            // onDelete={handleDeleteCategory}
          />
        </div>
      </div>
    </>
  );
};

export default CategoryWithSubdomain;
