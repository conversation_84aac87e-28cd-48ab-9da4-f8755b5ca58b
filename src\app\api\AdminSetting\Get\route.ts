import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function POST(req: NextRequest) {
    try {
        const user = await verifyToken(req);

        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const hasBody = req.headers.get('content-length') !== '0';
        const body = hasBody ? await req.json() : null;

        const selectFields = {
            Id: true,
            LmStyleId: true,
            DmStyleId: true,
            ChannalId: true,
            PubId: true,
            AdsAccountId: true,
            AdsClientId: true,
            HeadTagJSON: true,
            CreatedAt: true,
            UpdatedAt: true,
            CountAdsClick: true,
            CampaignClientId: true,
            CampaignClientSecret: true,
            RevenueClientSecret: true,
            RevenueClientId: true
        };

        // Check for existing admin setting
        const existingSetting = await prisma.adminUserSetting.findFirst({
            select: selectFields
        });

        // Create new setting if not found
        if (!existingSetting) {
            const newSetting = await prisma.adminUserSetting.create({
                data: {
                    LmStyleId: body?.LmStyleId ?? null,
                    DmStyleId: body?.DmStyleId ?? null,
                    ChannalId: body?.ChannalId ?? null,
                    PubId: body?.PubId ?? null,
                    AdsAccountId: body?.AdsAccountId ?? null,
                    AdsClientId: body?.AdsClientId ?? null,
                    HeadTagJSON: body?.HeadTagJSON ?? '{}',
                    CountAdsClick: body?.CountAdsClick ?? '',
                    CampaignClientId: body?.CampaignClientId ?? null,
                    CampaignClientSecret: body?.CampaignClientSecret ?? null,
                    RevenueClientSecret: body?.RevenueClientSecret ?? null,
                    RevenueClientId: body?.RevenueClientId ?? null
                },
                select: selectFields
            });

            return NextResponse.json({
                success: true,
                message: 'Admin setting created successfully',
                data: newSetting
            });
        }

        // If body exists, update the setting
        if (body) {
            const updatedSetting = await prisma.adminUserSetting.update({
                where: { Id: existingSetting.Id },
                data: {
                    LmStyleId: body.LmStyleId || existingSetting.LmStyleId,
                    DmStyleId: body.DmStyleId || existingSetting.DmStyleId,
                    ChannalId: body.ChannalId || existingSetting.ChannalId,
                    PubId: body.PubId || existingSetting.PubId,
                    AdsAccountId: body.AdsAccountId || existingSetting.AdsAccountId,
                    AdsClientId: body.AdsClientId || existingSetting.AdsClientId,
                    HeadTagJSON: body.HeadTagJSON ?? existingSetting.HeadTagJSON,
                    CountAdsClick: body.CountAdsClick ?? existingSetting.CountAdsClick,
                    CampaignClientId: body.CampaignClientId || existingSetting.CampaignClientId,
                    CampaignClientSecret: body.CampaignClientSecret || existingSetting.CampaignClientSecret,
                    RevenueClientSecret: body.RevenueClientSecret || existingSetting.RevenueClientSecret,
                    RevenueClientId: body.RevenueClientId || existingSetting.RevenueClientId
                },
                select: selectFields
            });

            return NextResponse.json({
                success: true,
                message: 'Admin setting updated successfully',
                data: updatedSetting
            });
        }

        // Return existing setting if no body provided
        return NextResponse.json({
            success: true,
            data: existingSetting
        });

    } catch (error) {
        console.error('Error handling admin user setting:', error);
        return NextResponse.json({
            error: 'Failed to process admin user setting',
            details: process.env.NODE_ENV === 'development' ? error : undefined
        }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}
