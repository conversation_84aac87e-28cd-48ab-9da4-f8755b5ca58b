import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogActions,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Icon<PERSON>utton,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import { CloseIcon } from "@/assets/icons";
import InputGroup from "@/components/FormElements/InputGroup";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import Checkbox from "@/components/FormElements/checkbox";
import dynamic from "next/dynamic";
import Swal from "sweetalert2";

const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });

const joditConfig = {
  height: 300,
  minHeight: 200,
  maxHeight: 900,
  uploader: {
    url: '/api/article/ImageUpload',
    insertImageAsBase64URI: false,
    headers: {
      'Accept': 'application/json',
      'Cache-Control': 'no-cache'
    },
    prepareData: (formData) => {
      const file = formData.get('files[0]');
      formData.append('Image', file);
      return formData;
    },
    isSuccess: (resp) => {
      let response = typeof resp === 'string' ? JSON.parse(resp) : resp;
      return response?.success === true;
    },
    getMessage: (resp) => {
      let response = typeof resp === 'string' ? JSON.parse(resp) : resp;
      return response?.message || 'Upload completed';
    },
    process: (resp) => {
      let response = typeof resp === 'string' ? JSON.parse(resp) : resp;
      if (!response?.imagePath) throw new Error("No imagePath in server response");
      
      const imagePath = response.imagePath.startsWith('/')
        ? response.imagePath
        : `/${response.imagePath}`;
        
      const fullUrl = `${imagePath}`;
      
      return {
        files: [fullUrl],
        path: imagePath,
        error: null,
        msg: response.message
      };
    },
    defaultHandlerSuccess: function (data, resp) {
      if (data.files && data.files.length) { 
        for (let i = 0; i < data.files.length; i++) {
          this.s.insertImage(data.files[i]);
        }
      }
    },
    error: function (e) {
      console.error('Upload error:', e);
      Swal.fire({
        icon: 'error',
        title: 'Upload Failed',
        html: `Failed to upload image<br><small>${e.message || e}</small>`,
        showConfirmButton: true
      });
    }
  },
  toolbarAdaptive: false,
  buttons: [
    "bold",
    "italic",
    "underline",
    "strikethrough",
    "|",
    "ul",
    "ol",
    "|",
    "outdent",
    "indent",
    "|",
    "font",
    "fontsize",
    "brush",
    "paragraph",
    "|",
    "image",
    "table",
    "link",
    "|",
    "align",
    "undo",
    "redo",
    "|",
    "hr",
    "eraser",
    "copyformat",
    "|",
    "source",
    {
      name: "insertRelatedSearch2",
      tooltip: "Insert Related Search 2 Div",
      exec: (editor) => {
        const editorContent = editor.value;
        if (editorContent.includes('id="relatedsearch2"')) {
          Swal.fire({
            title: 'Warning',
            text: 'Related Search 2 div already exists!',
            icon: 'warning',
            confirmButtonText: 'OK'
          });
          return;
        }
        
        const div = editor.createInside.element('div');
        div.id = 'relatedsearch2';
        
        const style = document.createElement('style');
        style.innerHTML = `
          #relatedsearch2 {
            background-color: #fff9c4 !important;
            padding: 10px !important;
            margin: 10px 0 !important;
          }
        `;
        editor.editorDocument.head.appendChild(style);
        
        editor.s.insertNode(div);
        editor.selection.select(div);
        
        editor.events.on('beforeDestruct', () => {
          if (style.parentNode) {
            style.parentNode.removeChild(style);
          }
        });
      },
    }
  ],
  style: {
    border: "1px solid #e5e7eb",
    borderRadius: "8px",
    overflowY: "auto",
  },
};

const shortJoditConfig = {
  height: 100,
  minHeight: 120,
  maxHeight: 350,
  uploader: { insertImageAsBase64URI: true },
  toolbarAdaptive: false,
  buttons: ["bold", "italic", "underline", "ul", "ol", "link", "erase"],
  style: {
    border: "1px solid #e5e7eb",
    borderRadius: "8px",
    overflowY: "auto",
  },
};

const ArticleModal = ({
  open,
  onClose,
  mode,
  formData,
  setFormData,
  description,
  setDescription,
  shortDescription,
  setShortDescription,
  formdataImage,
  setFormdataImage,
  base64Image,
  setBase64Image,
  published,
  setPublished,
  showArticle,
  setShowArticle,
  showAds,
  setShowAds,
  selectedDomain,
  setSelectedDomain,
  selectedSubdomain,
  setSelectedSubdomain,
  selectedCampaignIds,
  setSelectedCampaignIds,
  domains,
  subdomains,
  categories,
  assignChannels,
  styleIds,
  campaigns,
  editId,
  editSlugMode,
  setEditSlugMode,
  handleFormSubmit,
  handleChange,
  handleChannelSelect,
  handleCampaignSelect,
  validateField,
  autoslug,
  showLoader,
  showSecondRelatedSearch,
  setShowSecondRelatedSearch,
}) => {
  const handleSecondRelatedSearchToggle = () => {
    setShowSecondRelatedSearch(!showSecondRelatedSearch);
    setFormData((prev) => ({
      ...prev,
      adrelatedsearches2: { val: "", err: null },
    }));
  };

  const handleFormSubmitWithValidation = (e) => {
    e.preventDefault();
    if (showSecondRelatedSearch && formData.adrelatedsearches2?.val) {
      if (!description.includes('id="relatedsearch2"')) {
        Swal.fire({
          icon: "error",
          title: "Validation Error",
          text: "Related Search 2 div is missing in the description. Please add it using the editor button.",
        });
        return;
      }
    }
    handleFormSubmit(e);
  };

  const renderSlugField = () => (
    <div className="mb-4">
      <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
        URL
      </label>
      <div className="flex flex-wrap items-center gap-2">
        <span className="rounded-md border border-stroke bg-gray-100 px-4 py-3 text-dark dark:border-dark-3 dark:bg-dark-2 dark:text-white">
          {selectedDomain
            ? `https://${
                selectedSubdomain
                  ? `${subdomains.find((s) => s.Id === selectedSubdomain)?.Url || ""}.`
                  : ""
              }${domains.find((d) => d.Id === selectedDomain)?.ShowUrlName || ""}/`
            : `${process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE}/`}
        </span>
        <input
          type="text"
          name="url"
          value={formData.url.val.split("/").pop()}
          onChange={(e) => {
            // Allow Unicode characters but clean up unwanted symbols
            const slug = e.target.value
              .replace(/[^\p{L}\p{N}\s-]/gu, '') // Remove symbols except letters, numbers, hyphens, and spaces
              .trim()
              .replace(/\s+/g, '-'); // Replace spaces with hyphens
            
            handleChange({
              target: {
                name: "url",
                value: slug
              }
            });
          }}
          className="min-w-[200px] flex-1 rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20 dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
          readOnly={mode === "edit" && !editSlugMode} // Only readonly in edit mode when editSlugMode is false
        />
        
        {mode === "edit" && (
          <label className="flex items-center gap-2">
            <Checkbox
              label="Edit Slug"
              name="editSlug"
              checked={editSlugMode}
              onChange={() => setEditSlugMode(!editSlugMode)}
              withIcon="check"
              withBg
              radius="default"
            />
          </label>
        )}
      </div>
      {formData.url.err && (
        <p className="mt-1 text-sm text-red-500">{formData.url.err}</p>
      )}
    </div>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="xl"
      slotProps={{
        paper: { sx: { maxHeight: "90vh" } },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {mode === "edit" ? "Edit Article" : mode === "clone" ? "Clone Article" : "Add Article"}
        </span>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ color: "white" }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form onSubmit={handleFormSubmitWithValidation} className="flex flex-1 flex-col">
          <div className="mb-4 flex flex-col gap-4 lg:flex-row">
            <div className="z-50 w-full">
              <SearchableDropdown
                label="Domain"
                options={domains}
                placeholder="Select Domain..."
                value={selectedDomain}
                onChange={(domain) => {
                  setSelectedDomain(domain?.Id || "");
                  setSelectedSubdomain("");
                  setFormData((prev) => ({
                    ...prev,
                    domain: {
                      val: domain?.Id || "",
                      err: validateField("domain", domain?.Id || "", showAds),
                    },
                    subdomain: { val: "", err: "" },
                  }));
                }}
                error={formData.domain.err}
                displayKey="Name"
                idKey="Id"
                required
              />
            </div>
            <div className="z-40 w-full">
              <SearchableDropdown
                label="Subdomain"
                options={subdomains}
                placeholder="Select Subdomain..."
                value={selectedSubdomain}
                onChange={(subdomain) => {
                  setSelectedSubdomain(subdomain?.Id || "");
                  setFormData((prev) => ({
                    ...prev,
                    subdomain: {
                      val: subdomain?.Id || "",
                      err: showAds ? validateField("subdomain", subdomain?.Id || "", showAds) : "",
                    },
                  }));
                }}
                error={formData.subdomain.err}
                displayKey="Name"
                idKey="Id"
                disabled={!selectedDomain || !subdomains.length}
                required={showAds}
              />
            </div>
          </div>
          <div className="mb-4 flex flex-col gap-4 lg:flex-row">
            <div className="w-full">
              <InputGroup
                label="Article Title"
                name="title"
                type="text"
                placeholder="Enter Article Title"
                value={formData.title.val}
                handleChange={handleChange}
                required
                className="w-full"
                active={!!formData.title.err}
              />
              {formData.title.err && (
                <p className="mt-1 text-sm text-red-500">{formData.title.err}</p>
              )}
            </div>
            <div className="w-full">
              <SearchableDropdown
                label="Category"
                options={categories}
                placeholder="Select Category..."
                value={formData.category.val}
                onChange={(category) => {
                  setFormData((prev) => ({
                    ...prev,
                    category: {
                      val: category?.Id || "",
                      err: validateField("category", category?.Id || "", showAds),
                    },
                  }));
                }}
                error={formData.category.err}
                displayKey="Name"
                idKey="Id"
                required
              />
            </div>
          </div>
          {renderSlugField()}
          <div className="mb-4">
            <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
              Short Description
            </label>
            <JoditEditor
              value={shortDescription}
              config={shortJoditConfig}
              onBlur={(newContent) => {
                setShortDescription(newContent);
                setFormData((prev) => ({
                  ...prev,
                  shortDescription: {
                    val: newContent,
                    err: validateField("shortDescription", newContent, showAds),
                  },
                }));
              }}
              onChange={() => {}}
            />
            {formData.shortDescription.err && (
              <p className="mt-1 text-sm text-red-500">{formData.shortDescription.err}</p>
            )}
          </div>
          <div className="mb-4">
            <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
              Description
            </label>
            <JoditEditor
              value={description}
              config={joditConfig}
              onBlur={(newContent) => {
                setDescription(newContent);
                setFormData(prev => ({
                  ...prev,
                  description: {
                    val: newContent,
                    err: validateField("description", newContent, showAds),
                  },
                }));
              }}
              onChange={(newContent) => {
                setDescription(newContent);
              }}
              tabIndex={1}
            />
            {formData.description.err && (
              <p className="mt-1 text-sm text-red-500">{formData.description.err}</p>
            )}
          </div>
          <div className="mb-4 flex flex-col gap-4 sm:flex-row">
            <div className="w-full sm:w-1/3">
              <TextAreaGroup
                label="Meta Title"
                name="metatitle"
                value={formData.metatitle.val}
                handleChange={handleChange}
                placeholder="Enter Meta Title"
                rows={2}
              />
            </div>
            <div className="w-full sm:w-1/3">
              <TextAreaGroup
                label="Meta Description"
                name="metadescription"
                value={formData.metadescription.val}
                handleChange={handleChange}
                placeholder="Enter Meta Description"
                rows={2}
              />
            </div>
            <div className="w-full sm:w-1/3">
              <TextAreaGroup
                label="Meta Keys"
                name="metakeys"
                value={formData.metakeys.val}
                handleChange={handleChange}
                placeholder="Enter Meta Keys"
                rows={2}
              />
            </div>
          </div>
          <div className="mb-4">
            <TextAreaGroup
              label="Hashtag"
              name="hashtag"
              value={formData.hashtag.val}
              handleChange={(e) =>
                handleChange({
                  target: {
                    name: "hashtag",
                    value: e.target.value.replace(/[ ,]/g, "#"),
                  },
                })
              }
              placeholder="Enter Hashtags"
              rows={2}
            />
          </div>
          <div className="mb-4">
            <label className="flex items-center gap-2">
              <Checkbox
                label="Show Ads"
                name="showAds"
                checked={showAds}
                onChange={() => {
                  setShowAds(!showAds);
                  if (!showAds) {
                    setFormData((prev) => ({
                      ...prev,
                      customChannal: { val: "", err: "" },
                      styleIdLm: { val: "", err: "" },
                      styleIdDm: { val: "", err: "" },
                      campaigns: { val: [], err: "" },
                      adrelatedsearches: { val: "7", err: "" },
                      adrelatedsearches2: { val: "", err: "" },
                      subdomain: { val: prev.subdomain.val, err: validateField("subdomain", prev.subdomain.val, true) },
                    }));
                    setSelectedCampaignIds([]);
                    setShowSecondRelatedSearch(false);
                  } else {
                    setFormData((prev) => ({
                      ...prev,
                      subdomain: { val: prev.subdomain.val, err: "" },
                    }));
                  }
                }}
                withIcon="check"
                withBg
                radius="default"
              />
            </label>
          </div>
          {showAds && (
            <>
              <div className="mb-4 flex flex-col gap-4 lg:flex-row">
                <div className="z-50 w-full">
                  <SearchableDropdown
                    label="Channel Settings"
                    options={assignChannels}
                    placeholder="Select Channel..."
                    value={formData.customChannal.val}
                    onChange={handleChannelSelect}
                    error={formData.customChannal.err}
                    displayKey="DisplayName"
                    idKey="Id"
                    required
                  />
                </div>
                <div className="flex gap-4 w-full">
                  <div className="z-40 w-1/2">
                    <SearchableDropdown
                      label="Light Mode Style ID"
                      options={styleIds}
                      placeholder="Select Light Mode Style ID..."
                      value={formData.styleIdLm.val}
                      onChange={(style) => {
                        setFormData((prev) => ({
                          ...prev,
                          styleIdLm: {
                            val: style?.Id || "",
                            err: validateField("styleIdLm", style?.Id || "", showAds),
                          },
                        }));
                      }}
                      error={formData.styleIdLm.err}
                      displayKey="Name"
                      idKey="Id"
                    />
                  </div>
                  <div className="z-40 w-1/2">
                    <SearchableDropdown
                      label="Dark Mode Style ID"
                      options={styleIds}
                      placeholder="Select Dark Mode Style ID..."
                      value={formData.styleIdDm.val}
                      onChange={(style) => {
                        setFormData((prev) => ({
                          ...prev,
                          styleIdDm: {
                            val: style?.Id || "",
                            err: validateField("styleIdDm", style?.Id || "", showAds),
                          },
                        }));
                      }}
                      error={formData.styleIdDm.err}
                      displayKey="Name"
                      idKey="Id"
                    />
                  </div>
                </div>
              </div>
              <div className="z-30 mb-4">
                <MultiSelectDropdown
                  label="Campaigns"
                  options={campaigns}
                  value={selectedCampaignIds}
                  onChange={handleCampaignSelect}
                  placeholder="Search Campaigns..."
                  displayKey="Name"
                  idKey="SNo"
                  showSelectAll={true}
                  error={formData.campaigns.err}
                />
              </div>
              <div className="mb-4">
                <InputGroup
                  label="Ad Related Searches"
                  name="adrelatedsearches"
                  value={formData.adrelatedsearches.val}
                  handleChange={handleChange}
                  placeholder="Enter number of ads"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="flex items-center gap-2 mb-2">
                  <Checkbox
                    label="Add 2nd Related Search"
                    name="addSecondRelatedSearch"
                    checked={showSecondRelatedSearch}
                    onChange={handleSecondRelatedSearchToggle}
                    withIcon="check"
                    withBg
                    radius="default"
                  />
                </label>
                {showSecondRelatedSearch && (
                  <InputGroup
                    label="2nd Ad Related Searches"
                    name="adrelatedsearches2"
                    value={formData.adrelatedsearches2?.val || ""}
                    handleChange={handleChange}
                    placeholder="Enter number of 2nd related ads"
                    required={showSecondRelatedSearch}
                  />
                )}
                {showSecondRelatedSearch && formData.adrelatedsearches2?.err && (
                  <p className="mt-1 text-sm text-red-500">{formData.adrelatedsearches2.err}</p>
                )}
              </div>
            </>
          )}
          <div className="mb-4">
            <InputGroup
              label="Read Time (minutes)"
              name="readTime"
              value={formData.readTime.val}
              handleChange={handleChange}
              placeholder="Enter read time in minutes"
              required
              min="0"
            />
            {formData.readTime.err && (
              <p className="mt-1 text-sm text-red-500">{formData.readTime.err}</p>
            )}
          </div>
          <div className="mb-4">
            <TextAreaGroup
              label="Remark"
              name="remark"
              value={formData.remark.val}
              handleChange={handleChange}
              placeholder="Enter Remark"
              rows={3}
            />
          </div>
          <div className="mb-4">
            <InputGroup
              type="file"
              name="Image"
              handleChange={handleChange}
              fileStyleVariant="style1"
              label="Image"
              placeholder="Image"
            />
            {(base64Image || (typeof formdataImage === "string" && formdataImage)) && (
              <div className="mt-4">
                <img
                  src={base64Image || formdataImage}
                  alt="Preview"
                  className="h-32 w-32 rounded object-cover"
                  width={128}
                  height={128}
                />
              </div>
            )}
          </div>
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <label className="flex items-center gap-2">
              <Checkbox
                label="Published"
                name="published"
                checked={published}
                onChange={() => setPublished(!published)}
                withIcon="check"
                withBg
                radius="default"
              />
            </label>
            <label className="flex items-center gap-2">
              <Checkbox
                label="Show Article"
                name="showArticle"
                checked={showArticle}
                onChange={() => setShowArticle(!showArticle)}
                withIcon="check"
                withBg
                radius="default"
              />
            </label>
          </div>
        </form>
      </DialogContent>

      <DialogActions sx={{ py: 2, px: 3 }}>
        <Button
          type="button"
          label="Cancel"
          onClick={onClose}
          variant="dark"
          shape="rounded"
        />
        <Button
          type="submit"
          label={mode === "edit" ? "Update Article" : mode === "clone" ? "Clone Article" : "Add Article"}
          variant="primary"
          shape="rounded"
          disabled={showLoader}
          onClick={handleFormSubmitWithValidation}
        />
      </DialogActions>
    </Dialog>
  );
};

export default ArticleModal;