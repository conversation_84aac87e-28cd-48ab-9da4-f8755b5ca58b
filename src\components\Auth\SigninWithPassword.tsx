"use client";
import React, { useState } from "react";
import { toast } from 'react-toastify';
import { EmailIcon } from "@/assets/icons";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import axios from 'axios';
import Link from "next/link";
import InputGroup from "../FormElements/InputGroup";

export default function SigninWithPassword() {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [data, setData] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});



  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    setData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Real-time validation
    let error = "";

    if (name === "email") {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!value) {
        error = "Email is required*";
      } else if (!emailRegex.test(value)) {
        error = "Invalid email format";
      }
    }

    if (name === "password") {
      if (!value) {
        error = "Password is required*";
      } else if (value.length < 6) {
        error = "Password must be at least 6 characters";
      }
    }

    setErrors(prev => ({
      ...prev,
      [name]: error,
    }));
  };


  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (loading) return;
    setLoading(true);

    if (errors.email || errors.password) {
      toast.error("Please fix the errors before submitting");
      setLoading(false);
      return;
    }

    try {
      const response = await axios.post("/api/auth/login", {
        email: data.email,
        password: data.password,
      });
      if (response && response.data && response.data.success) {
        localStorage.setItem("accessToken", response.data.accessToken);
        window.location.href = '/';
      } else {
        toast.error('Login failed: Invalid response from server');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputGroup
        type="email"
        label="Email"
        className="mb-4 [&_input]:py-[15px]"
        placeholder="Enter your email"
        name="email"
        handleChange={handleChange}
        value={data.email}
        error={errors.email}
        icon={<EmailIcon />}
      />

      <InputGroup
        type={showPassword ? "text" : "password"}
        label="Password"
        className="mb-5 [&_input]:py-[15px]"
        placeholder="Enter your password"
        name="password"
        handleChange={handleChange}
        value={data.password}
        error={errors.password}
        icon={
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            {showPassword ? <FaEyeSlash /> : <FaEye />}
          </button>
        }
        iconPosition="right"
      />

      <div className="mb-5 text-right">
        <Link href="/auth/forgot-password" className="text-primary">
          Forgot password?
        </Link>
      </div>

      <div className="mb-4.5">
        <button
          type="submit"
          className="flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg bg-primary p-4 font-medium text-white transition hover:bg-opacity-90"
          disabled={loading}
        >
          Sign In
          {loading && (
            <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-white border-t-transparent dark:border-primary dark:border-t-transparent" />
          )}
        </button>
      </div>
    </form>
  );
}