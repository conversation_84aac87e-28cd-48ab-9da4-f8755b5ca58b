"use client";
import * as React from "react";
import { visuallyHidden } from "@mui/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  Box,
  Paper,
} from "@mui/material";
import Tooltip from "@mui/material/Tooltip";
import CircularProgress from "@mui/material/CircularProgress";
import { FaEye, FaEdit, FaTrash, FaClone, FaPlus, FaUsers } from "react-icons/fa";
import { MdBlock } from "react-icons/md";
import { CgUnblock } from "react-icons/cg";
import IconButton from "@mui/material/IconButton";
import LockResetIcon from "@mui/icons-material/LockReset";
import FirstPageIcon from "@mui/icons-material/FirstPage";
import KeyboardArrowLeft from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight";
import LastPageIcon from "@mui/icons-material/LastPage";
import { useTheme } from "@mui/material/styles";

function TablePaginationActions(props) {
  const theme = useTheme();
  const { count, page, rowsPerPage, onPageChange } = props;

  const handleFirstPageButtonClick = (event) => {
    onPageChange(event, 0);
  };

  const handleBackButtonClick = (event) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event) => {
    onPageChange(event, page + 1);
  };

  const handleLastPageButtonClick = (event) => {
    onPageChange(event, Math.max(0, Math.ceil(count / rowsPerPage) - 1));
  };

  return (
    <Box sx={{ flexShrink: 0, ml: 2.5 }}>
      <IconButton
        onClick={handleFirstPageButtonClick}
        disabled={page === 0}
        aria-label="first page"
      >
        {theme.direction === "rtl" ? <LastPageIcon /> : <FirstPageIcon />}
      </IconButton>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 0}
        aria-label="previous page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowRight />
        ) : (
          <KeyboardArrowLeft />
        )}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage) - 1}
        aria-label="next page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowLeft />
        ) : (
          <KeyboardArrowRight />
        )}
      </IconButton>
      <IconButton
        onClick={handleLastPageButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage) - 1}
        aria-label="last page"
      >
        {theme.direction === "rtl" ? <FirstPageIcon /> : <LastPageIcon />}
      </IconButton>
    </Box>
  );
}

function EnhancedTableHead(props) {
  const { order, orderBy, onRequestSort, headCells } = props;
  const createSortHandler = (property) => (event) => {
    onRequestSort(event, property);
  };

  return (
    <TableHead>
      <TableRow>
        {headCells.map((col) => (
          <TableCell
            key={col.id}
            align={col.id === "sr" ? "right" : col.id === "actions" ? "center" : "left"}
            padding="normal"
            sortDirection={orderBy === col.id ? order : false}
            sx={{
              backgroundColor: "#635de7",
              color: "white",
              fontWeight: 700,
              width: col.id === "sr" ? "5%" : col.id === "actions" ? "10%" : "auto",
            }}
          >
            {col.id !== "actions" && col.id !== "sr" ? (
              <TableSortLabel
                active={orderBy === col.id}
                direction={orderBy === col.id ? order : "asc"}
                onClick={createSortHandler(col.id)}
              >
                {col.label}
                {orderBy === col.id ? (
                  <Box component="span" sx={visuallyHidden}>
                    {order === "desc"
                      ? "sorted descending"
                      : "sorted ascending"}
                  </Box>
                ) : null}
              </TableSortLabel>
            ) : (
              col.label
            )}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

export default function CustomDataTable({
  isLoading,
  columns,
  rows,
  page,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  totalCount,
  order,
  orderBy,
  onRequestSort,
  onEdit,
  onClone,
  onDelete,
  onBlock,
  notShowAction = false,
  onReset,
  handleSubDomainClick,
  handleStyleIDClick,
  handleUserCountClick,
  handleViewCampaignsClick,
  handleAddCampaignClick,
  onView,
  totals,
}) {
  const headCells = [
    { id: "sr", label: "Sr No" },
    ...columns,
    ...(notShowAction ? [] : [{ id: "actions", label: "Actions" }]),
  ];

  return (
    <Box sx={{ width: "100%" }}>
    <Paper sx={{ width: "100%" }} elevation={0}>
      <TableContainer sx={{ overflowX: "auto" }}>
        <Table sx={{ minWidth: 750 }} aria-labelledby="tableTitle" size="medium">
          <EnhancedTableHead
            order={order}
            orderBy={orderBy}
            headCells={headCells}
            onRequestSort={onRequestSort}
          />
          
          {/* Add Totals Row */}
          {totals?.cells?.length > 0 && (
            <TableBody>
              <TableRow sx={{ backgroundColor: "#f3f4f6", fontWeight: 'bold' }}>
                <TableCell 
                  align="right" 
                  sx={{ 
                    borderBottom: "none", 
                    fontWeight: 700,
                    backgroundColor: "#f3f4f6"
                  }}
                >
                  Totals
                </TableCell>
                {columns.map((col, colIndex) => (
                  <TableCell
                    key={`total-${col.id}`}
                    align={col.id === "sr" ? "right" : "left"}
                    sx={{
                      borderBottom: "none",
                      fontWeight: 700,
                      backgroundColor: "#f3f4f6"
                    }}
                  >
                    {totals.cells[colIndex]?.value || ''}
                  </TableCell>
                ))}
              </TableRow>
            </TableBody>
          )}
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={columns.length + 2} align="center">
                    <CircularProgress size={40} />
                  </TableCell>
                </TableRow>
              ) : rows?.length > 0 ? (
                rows?.map((row, index) => (
                  <TableRow
                    hover
                    tabIndex={-1}
                    key={row.Id || index}
                    sx={{
                      backgroundColor: index % 2 === 0 ? "#f9fafb" : "#ffffff",
                      "&:hover td": { backgroundColor: "#e0e7ff" },
                    }}
                  >
                    <TableCell
                      align="right"
                      sx={{ borderBottom: "none", fontWeight: 500 }}
                    >
                      {page * rowsPerPage + index + 1}
                    </TableCell>
                    {columns.map((col) => {
                      let cellValue = row[col.id];
                      if (col.id === "showAds") {
                        const isShown = String(row.showAds).toLowerCase() === "true";
                        cellValue = (
                          <span
                            className={`rounded-full px-2 py-1 text-xs font-semibold ${isShown
                              ? "bg-green-200 text-green-800"
                              : "bg-red-200 text-red-800"
                              }`}
                          >
                            {isShown ? "Yes" : "No"}
                          </span>
                        );
                      }
                      if (col.id === "status") {
                        const isShown = String(row.published).toLowerCase() === "true";
                        cellValue = (
                          <span
                            className={`rounded-full px-2 py-1 text-xs font-semibold ${isShown
                              ? "bg-green-200 text-green-800"
                              : "bg-gray-300 text-black"
                              }`}
                          >
                            {isShown ? "Published" : "Draft"}
                          </span>
                        );
                      }
                      if (col.id === "Status") {
                        cellValue = row.Status ? (
                          <span className="rounded-full bg-green-200 px-2 py-1 text-xs font-semibold text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="rounded-full bg-red-200 px-2 py-1 text-xs font-semibold text-red-800">
                            Inactive
                          </span>
                        );
                      } else if (
                        col.id === "UserCount" ||
                        col.id === "subDomainCount" ||
                        col.id === "styleIdCount"
                      ) {
                        const handleClick =
                          col.id === "UserCount"
                            ? () => handleUserCountClick?.(row)
                            : col.id === "subDomainCount"
                              ? () => handleSubDomainClick?.(row)
                              : () => handleStyleIDClick?.(row);
                        const countLabel =
                          col.id === "UserCount"
                            ? "users"
                            : col.id === "subDomainCount"
                              ? "domains"
                              : "styles";
                        cellValue = (
                          <div className="flex items-center gap-2">
                            <span className="font-medium">
                              {row[col.id]}
                            </span>
                            <Tooltip title={`View ${countLabel}`} arrow>
                              <button
                                className="flex h-8 w-8 items-center justify-center rounded-lg border-2 border-purple-500 hover:bg-purple-600"
                                onClick={handleClick}
                              >
                                <FaUsers className="h-4 w-4 text-purple-500 hover:text-white" />
                              </button>
                            </Tooltip>
                          </div>
                        );
                      } else if (col.id === "campaignCount") {
                        cellValue = (
                          <div className="flex items-center justify-center gap-2">
                            <div className="h-8">
                              <Tooltip title="View Campaigns" arrow>
                                <button
                                  className={`h-8 px-2 text-sm font-medium border rounded transition
                                     ${
                                      'text-purple-600 border-purple-500 hover:bg-purple-600 hover:text-white'
                                      
                                    }`}
                                  onClick={() => handleViewCampaignsClick?.(row)}
                                  aria-label="View Campaigns"
                                >
                                  {row.campaignCount || 0}
                                </button>
                              </Tooltip>
                            </div>
                            <Tooltip title="Add Campaign" arrow>
                              <button
                                className="flex h-8 w-8 items-center justify-center rounded border border-purple-500 hover:bg-purple-600 hover:text-white transition"
                                onClick={() => handleAddCampaignClick?.(row)}
                                aria-label="Add Campaign"
                              >
                                <FaPlus className="text-purple-500 hover:text-white" />
                              </button>
                            </Tooltip>
                          </div>
                        );
                      }
                      return (
                        <TableCell
                          key={col.id}
                          sx={{ borderBottom: "none" }}
                          align="left"
                        >
                          {cellValue}
                        </TableCell>
                      );
                    })}
                    {!notShowAction && (
                      <TableCell
                        align="center"
                        sx={{ borderBottom: "none", minWidth: 120 }}
                      >
                        <div className="flex items-center justify-center gap-3">
                          {onView && (
                            <Tooltip title="View" arrow>
                              <button
                                onClick={() => onView?.(row)}
                                className="text-blue-600 transition-colors duration-150 hover:text-blue-800"
                                aria-label="View"
                              >
                                <FaEye />
                              </button>
                            </Tooltip>
                          )}
                          {onBlock && (
                            <Tooltip
                              title={row.Block ? "Block User" : "Unblock User"}
                              arrow
                            >
                              <button
                                onClick={() => onBlock?.(row)}
                                className={
                                  row.Block
                                    ? "text-grey-600 hover:text-grey-800 transition-colors duration-150"
                                    : "text-green-600 transition-colors duration-150 hover:text-green-800"
                                }
                                aria-label={
                                  row.Block ? "Block User" : "Unblock User"
                                }
                              >
                                {row.Block ? (
                                  <MdBlock size={18} />
                                ) : (
                                  <CgUnblock size={18} />
                                )}
                              </button>
                            </Tooltip>
                          )}
                          {onEdit && (
                            <Tooltip title="Edit" arrow>
                              <button
                                onClick={() => onEdit?.(row)}
                                className="text-yellow-600 transition-colors duration-150 hover:text-yellow-800"
                                aria-label="Edit"
                              >
                                <FaEdit />
                              </button>
                            </Tooltip>
                          )}
                          {onClone && (
                            <Tooltip title="Clone" arrow>
                              <button
                                onClick={() => onClone?.(row)}
                                className="text-blue-600 transition-colors duration-150 hover:text-blue-800"
                                aria-label="Clone"
                              >
                                <FaClone />
                              </button>
                            </Tooltip>
                          )}
                          {onDelete && (
                            <Tooltip title="Delete" arrow>
                              <button
                                onClick={() => onDelete?.(row)}
                                className="text-red-600 transition-colors duration-150 hover:text-red-800"
                                aria-label="Delete"
                              >
                                <FaTrash />
                              </button>
                            </Tooltip>
                          )}
                          {onReset && (
                            <Tooltip title="Reset Password" arrow>
                              <button
                                onClick={() => onReset?.(row)}
                                className="text-purple-500 transition-colors duration-150 hover:text-purple-800"
                                aria-label="Reset Password"
                              >
                                <LockResetIcon />
                              </button>
                            </Tooltip>
                          )}
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length + 2}
                    align="center"
                    sx={{ fontWeight: 600 }}
                  >
                    No Data found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TablePagination
                  count={totalCount}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  ActionsComponent={TablePaginationActions}
                  onPageChange={(e, newPage) => onPageChange(newPage)}
                  onRowsPerPageChange={(e) =>
                    onRowsPerPageChange(parseInt(e.target.value))
                  }
                  rowsPerPageOptions={[10, 25, 50, { label: "All", value: -1 }]}
                />
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
}