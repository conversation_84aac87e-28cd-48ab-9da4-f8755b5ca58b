import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { writeFile, access, constants } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { mkdirSync, existsSync } from 'fs';
import sharp from 'sharp';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || '/var/www/images/categories';
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MIN_FILE_SIZE = 1024; // 1KB minimum file size

type SharpImageFormat = keyof sharp.FormatEnum;

export async function POST(req: NextRequest) {
    const user = await verifyToken(req);

    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        // Ensure upload directory exists and is writable
        if (!existsSync(UPLOAD_DIR)) {
            mkdirSync(UPLOAD_DIR, { recursive: true });
        }
        try {
            await access(UPLOAD_DIR, constants.W_OK);
        } catch (err) {
            console.error('Upload directory is not writable:', UPLOAD_DIR);
            return NextResponse.json(
                { error: 'Server configuration error: upload directory not writable' },
                { status: 500 }
            );
        }

        const formData = await req.formData();

        // Get text fields
        const name = formData.get('name')?.toString();
        const title = formData.get('title')?.toString();
        const shortDescription = formData.get('shortDescription')?.toString() || null;
        const imageFile = formData.get('image') as File | null;

        if (!name) {
            return NextResponse.json(
                { error: 'Missing required fields: name is mandatory.' },
                { status: 400 }
            );
        }

        // Generate ShowUrlName
        const showUrlName = name.replace(/[^a-zA-Z0-9]+/g, '-').toLowerCase();

        // Check for duplicate category name
        const existingCategory = await prisma.category.findFirst({
            where: {
                Name: name,
                OR: [
                    { IsDeleted: false },
                    { IsDeleted: null }
                ]
            },
        });

        if (existingCategory) {
            return NextResponse.json(
                { error: 'Category name already exists' },
                { status: 409 }
            );
        }

        // Helper function to validate image formats
        function isSharpImageFormat(format: string): format is SharpImageFormat {
            const sharpFormats: SharpImageFormat[] = ['jpeg', 'png', 'webp', 'gif', 'tiff', 'raw'];
            return sharpFormats.includes(format as SharpImageFormat);
        }

        // Process image upload
        let imagePath: string | null = null;

        if (imageFile && imageFile.size > 0) {
            try {
                console.log(`Processing category image (${imageFile.size} bytes, ${imageFile.name})`);

                const buffer = Buffer.from(await imageFile.arrayBuffer());
                let mimeType = imageFile.type;
                let originalExtension: SharpImageFormat = 'jpeg'; // default fallback

                // Handle cases where browser doesn't detect MIME type
                if (mimeType === 'application/octet-stream') {
                    try {
                        const metadata = await sharp(buffer).metadata();
                        if (metadata.format && isSharpImageFormat(metadata.format)) {
                            mimeType = `image/${metadata.format}`;
                            originalExtension = metadata.format;
                            console.log(`Detected actual image type: ${mimeType}`);
                        }
                    } catch (sharpError) {
                        console.log('Could not determine image type:', sharpError);
                        throw new Error('Invalid image format');
                    }
                } else {
                    const ext = mimeType.split('/')[1];
                    if (ext && isSharpImageFormat(ext)) {
                        originalExtension = ext;
                    }
                }

                // Validate file type
                if (!ALLOWED_IMAGE_TYPES.includes(mimeType)) {
                    throw new Error(`Invalid file type: ${mimeType}`);
                }

                // Validate file size
                if (imageFile.size < MIN_FILE_SIZE) {
                    throw new Error(`File too small: ${imageFile.size} bytes`);
                }

                const uniqueId = uuidv4();
                const baseFileName = `category_${uniqueId}.${originalExtension}`;

                // Process and save images
                const [originalBuffer, thumbnailBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, 300, { fit: 'cover', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                await Promise.all([
                    writeFile(path.join(UPLOAD_DIR, baseFileName), originalBuffer),
                    writeFile(path.join(UPLOAD_DIR, `thumb_${uniqueId}.${originalExtension}`), thumbnailBuffer)
                ]);

                imagePath = baseFileName;
            } catch (error) {
                console.error('Error processing category image:', error);
                return NextResponse.json(
                    { error: 'Failed to process image. Please upload a valid image file.' },
                    { status: 400 }
                );
            }
        }

        // Create new category
        const newCategory = await prisma.category.create({
            data: {
                Name: name,
                ShowUrlName: showUrlName,
                Title: title,
                Image: imagePath,
                ShortDescription: shortDescription,
            },
        });

        return NextResponse.json({
            success: true,
            data: {
                ...newCategory,
                imageUrl: imagePath ? `/images/categories/${imagePath}` : null,
            },
        });

    } catch (err) {
        console.error('Add Category Error:', err);
        return NextResponse.json(
            { error: err instanceof Error ? err.message : 'Internal Server Error' },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}