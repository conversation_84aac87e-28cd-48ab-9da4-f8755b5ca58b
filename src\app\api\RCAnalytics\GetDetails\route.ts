import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

interface RevenueCampaignData {
    articleid?: string;
    articlename?: string;
    date?: string;
    rv_revenue: number;
    rv_rpc: number;
    profit: number;
    roi: number;
    ads_conversions: number;
    ads_click: number;
    ads_spend: number;
    ads_cpa: number;
    ads_cpc: number;
    channelid?: string;
    channelname?: string;
}

const safeJson = (obj: any): any => {
    if (Array.isArray(obj)) {
        return obj.map(safeJson);
    } else if (obj !== null && typeof obj === 'object') {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [key, safeJson(value)])
        );
    } else if (typeof obj === 'bigint') {
        return obj.toString();
    } else {
        return obj;
    }
};

export async function POST(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const url = new URL(req.url);
        const searchParams = url.searchParams;

        // Get parameters from URL
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const showNoRevenue = searchParams.get('showNoRevenue') === 'true';
        const orderBy = searchParams.get('orderBy') || 'ads_spend';
        const orderDir = searchParams.get('orderDir') || 'desc';

        // Get parameters from request body
        const body = await req.json();
        const { articleId, channelId, groupBy } = body;

        if (!startDate || !endDate) {
            return NextResponse.json({ error: 'Both startDate and endDate are required' }, { status: 400 });
        }

        const validSortFields = [
            'ads_spend', 'rv_revenue', 'profit', 'roi',
            'ads_conversions', 'ads_click', 'ads_cpa', 'ads_cpc',
            'articlename', 'channelname', 'country', 'date',
            'rv_rpc'
        ];

        if (!validSortFields.includes(orderBy.toLowerCase())) {
            return NextResponse.json({
                error: `Invalid orderBy parameter. Valid options: ${validSortFields.join(', ')}`
            }, { status: 400 });
        }

        if (!['asc', 'desc'].includes(orderDir.toLowerCase())) {
            return NextResponse.json({ error: 'Invalid orderDir parameter. Use asc or desc' }, { status: 400 });
        }

        const processedArticleId = articleId ? (Array.isArray(articleId) ? articleId.join(',') : articleId.toString()) : null;
        const processedChannelId = channelId ? (Array.isArray(channelId) ? channelId.join(',') : channelId.toString()): null;
        const processedGroupBy = (groupBy || 'article').toString().toLowerCase();

        await prisma.$executeRawUnsafe(
            `SELECT * FROM fn_ads_revenue_data_test(
                $1::text, $2::text, $3::date, $4::date, $5::text, $6::boolean
            )`,
            processedArticleId,
            processedChannelId,
            new Date(startDate),
            new Date(endDate),
            processedGroupBy,
            showNoRevenue
        );

        const groupByFields = processedGroupBy.split(',').map((field: string) => field.trim());

        // Build select fields array
        const selectFieldsArray: string[] = [];
        const groupByFieldsArray: string[] = [];

        // Add date range label
        const dateRangeLabel = `${startDate} to ${endDate}`;
        selectFieldsArray.push(`'${dateRangeLabel}' as "date"`);

        if (groupByFields.includes('article')) {
            selectFieldsArray.push(`"articleid"::text as "articleid"`);
            selectFieldsArray.push(`"article_name"::text as "articlename"`);
            groupByFieldsArray.push('"articleid"', '"article_name"');
        }

        if (groupByFields.includes('channel')) {
            selectFieldsArray.push(`"channelid"::text as "channelid"`);
            selectFieldsArray.push(`"channel_name"::text as "channelname"`);
            groupByFieldsArray.push('"channelid"', '"channel_name"');
        }

        // if (groupByFields.includes('country')) {
        //     groupByFieldsArray.push('"country"');
        // }

        // Add aggregation fields
        selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) as "rv_revenue"`);
        selectFieldsArray.push(`CASE 
            WHEN COALESCE(SUM("ads_click"::integer), 0) = 0 THEN 0 
            ELSE COALESCE(SUM("rv_revenue"::float), 0) / COALESCE(SUM("ads_click"::integer), 1) 
        END as "rv_rpc"`);
        selectFieldsArray.push(`COALESCE(SUM("profit"::float), 0) as "profit"`);
        selectFieldsArray.push(`CASE 
            WHEN COALESCE(SUM("ads_spend"::float), 0) = 0 THEN 0 
            ELSE (COALESCE(SUM("profit"::float), 0) / COALESCE(SUM("ads_spend"::float), 1)) * 100 
        END as "roi"`);
        selectFieldsArray.push(`COALESCE(SUM("ads_conversions"::integer), 0) as "ads_conversions"`);
        selectFieldsArray.push(`COALESCE(SUM("ads_click"::integer), 0) as "ads_click"`);
        selectFieldsArray.push(`COALESCE(SUM("ads_spend"::float), 0) as "ads_spend"`);
        selectFieldsArray.push(`CASE 
            WHEN COALESCE(SUM("ads_conversions"::integer), 0) = 0 THEN 0 
            ELSE COALESCE(SUM("ads_spend"::float), 0) / COALESCE(SUM("ads_conversions"::integer), 1) 
        END as "ads_cpa"`);
        selectFieldsArray.push(`CASE 
            WHEN COALESCE(SUM("ads_click"::integer), 0) = 0 THEN 0 
            ELSE COALESCE(SUM("ads_spend"::float), 0) / COALESCE(SUM("ads_click"::integer), 1) 
        END as "ads_cpc"`);

        const selectFields = selectFieldsArray.join(',\n            ');
        const groupByClause = groupByFieldsArray.length > 0 ? `GROUP BY ${groupByFieldsArray.join(', ')}` : '';

        // Build ORDER BY clause with defaults
        const orderByClauseArray = [`"${orderBy}" ${orderDir}`];
        if (groupByFields.includes('article') && orderBy !== 'articlename') {
            orderByClauseArray.push('"article_name"');
        }
        if (groupByFields.includes('channel') && orderBy !== 'channelname') {
            orderByClauseArray.push('"channel_name"');
        }

        const query = `
            SELECT ${selectFields}
            FROM pg_temp.temp_ads_result
            ${groupByClause}
            ORDER BY ${orderByClauseArray.join(', ')}
        `;

        const rawData = await prisma.$queryRawUnsafe<RevenueCampaignData[]>(query);

        return NextResponse.json({
            success: true,
            data: safeJson(rawData),
        });

    } catch (error) {
        console.error('Error fetching revenue campaign data:', error);
        return NextResponse.json(
            {
                error: 'Failed to fetch revenue campaign data',
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}