import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "asc";
        const userId = searchParams.get("Id");

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        } else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        if (!userId) {
            return NextResponse.json(
                { error: "userId parameter is required" },
                { status: 400 }
            );
        }

        // Base filter
        let where: any = {
            UserId: userId,
            StyleIds: {
                IsDeleted: false
            }
        };

        // Handle search
        if (search && search !== "") {
            where = {
                ...where,
                StyleIds: {
                    ...where.StyleIds,
                    OR: [
                        { StyleId: { contains: search, mode: 'insensitive' } },
                        { Name: { contains: search, mode: 'insensitive' } },
                        { Prefix: { contains: search, mode: 'insensitive' } }
                    ]
                }
            };
        }

        // Proper orderBy implementation
        let orderByClause: any = {};
        const orderField = orderBy.toLowerCase();
        const direction = orderDir.toLowerCase() as 'asc' | 'desc';

        switch (orderField) {
            case "name":
                orderByClause = { StyleIds: { Name: direction } };
                break;
            case "styleid":
                orderByClause = { StyleIds: { StyleId: direction } };
                break;
            case "prefix":
                orderByClause = { StyleIds: { Prefix: direction } };
                break;
            case "createdat":
                orderByClause = { CreatedAt: direction };
                break;
            default:
                orderByClause = { [orderField]: direction };
        }

        // Get counts
        const recordsTotal = await prisma.styleIdUserMappings.count({ where });
        const recordsFiltered = recordsTotal; // Same in this case since we're not doing separate filtering

        // Get paginated data
        const mappings = await prisma.styleIdUserMappings.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            include: {
                StyleIds: {
                    select: {
                        StyleId: true,
                        Name: true,
                        Prefix: true,
                    }
                }
            }
        });

        // Transform data
        const transformedData = mappings.map(mapping => ({
            Id: mapping.Id,
            CreatedAt: mapping.CreatedAt,
            ...(mapping.StyleIds ? {
                StyleId: mapping.StyleIds.StyleId,
                Name: mapping.StyleIds.Name,
                Prefix: mapping.StyleIds.Prefix
            } : {})
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                recordsTotal,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}