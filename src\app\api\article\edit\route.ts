import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { Prisma } from '@prisma/client';

// const UPLOAD_DIR = path.join(process.cwd(), 'public/uploads');
const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

export async function PUT(req: NextRequest) {
    type AuthenticatedUser = {
        Id: string;
    };
    const user = await verifyToken(req) as AuthenticatedUser;
    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        const formData = await req.formData();

        // Extract all form fields
        const formFields = {
            Id: formData.get('Id'),
            Title: formData.get('Title'),
            Category: formData.get('Category'),
            Url: formData.get('Url'),
            Description: formData.get('Description'),
            Published: formData.get('Published'),
            ShowArticle: formData.get('ShowArticle'),
            ShowsAds: formData.get('ShowsAds'),
            MetaTitle: formData.get('MetaTitle'),
            MetaDescription: formData.get('MetaDescription'),
            MetaKeys: formData.get('MetaKeys'),
            CustomChannal: formData.get('CustomChannal'),
            StyleIdLm: formData.get('StyleIdLm'),
            StyleIdDm: formData.get('StyleIdDm'),
            AdRelatedSearches: formData.get('AdRelatedSearches'),
            Remark: formData.get('Remark'),
            ShortDescription: formData.get('ShortDescription'),
            Domain: formData.get('Domain'),
            SubDomain: formData.get('SubDomain'),
            CampaignIds: formData.get('CampaignIds'),
            file: formData.get('file'),
            ReadTime: formData.get('ReadTime'),
            CheckRelatedSearches2: formData.get('CheckRelatedSearches2'),
            AdReletadSearches2: formData.get('AdReletadSearches2')
        };

        // Validate required ID
        if (!formFields.Id?.toString().trim()) {
            return NextResponse.json(
                { error: "Article ID is required" },
                { status: 400 }
            );
        }

        // Check if article exists
        const existingArticle = await prisma.articleDetails.findUnique({
            where: { Id: formFields.Id.toString() }
        });

        if (!existingArticle) {
            return NextResponse.json(
                { error: "Article not found" },
                { status: 404 }
            );
        }

        // Process text fields - now handles blank values properly
        const textFields = {
            Id: formFields.Id.toString(),
            // For string fields: if field exists in form data, use it (even if blank), otherwise skip
            Title: formFields.Title !== null ? formFields.Title.toString() : undefined,
            Category: formFields.Category !== null ? formFields.Category.toString() : undefined,
            Url: formFields.Url !== null ? (formFields.Url.toString().trim() || null) : undefined,
            Description: formFields.Description !== null ? (formFields.Description.toString().trim() || null) : undefined,
            MetaTitle: formFields.MetaTitle !== null ? (formFields.MetaTitle.toString().trim() || null) : undefined,
            MetaDescription: formFields.MetaDescription !== null ? (formFields.MetaDescription.toString().trim() || null) : undefined,
            MetaKeys: formFields.MetaKeys !== null ? (formFields.MetaKeys.toString().trim() || null) : undefined,
            CustomChannal: formFields.CustomChannal !== null ? (formFields.CustomChannal.toString().trim() || null) : undefined,
            StyleIdLm: formFields.StyleIdLm !== null ? (formFields.StyleIdLm.toString().trim() || null) : undefined,
            StyleIdDm: formFields.StyleIdDm !== null ? (formFields.StyleIdDm.toString().trim() || null) : undefined,
            AdRelatedSearches: formFields.AdRelatedSearches !== null ? (formFields.AdRelatedSearches.toString().trim() || null) : undefined,
            Remark: formFields.Remark !== null ? (formFields.Remark.toString().trim() || null) : undefined,
            ShortDescription: formFields.ShortDescription !== null ? (formFields.ShortDescription.toString().trim() || null) : undefined,
            Domain: formFields.Domain !== null ? (formFields.Domain.toString().trim() || null) : undefined,
            SubDomain: formFields.SubDomain !== null ? (formFields.SubDomain.toString().trim() || null) : undefined,
            AdReletadSearches2: formFields.AdReletadSearches2 !== null ? (formFields.AdReletadSearches2.toString().trim() || null) : undefined,

            // For boolean fields
            Published: formFields.Published !== null ? formFields.Published === 'true' : undefined,
            ShowArticle: formFields.ShowArticle !== null ? formFields.ShowArticle === 'true' : undefined,
            ShowsAds: formFields.ShowsAds !== null ? formFields.ShowsAds === 'true' : undefined,
            CheckRelatedSearches2: formFields.CheckRelatedSearches2 !== null ? formFields.CheckRelatedSearches2 === 'true' : undefined,

            // For numeric fields
            ReadTime: formFields.ReadTime !== null ?
                (formFields.ReadTime.toString().trim() === '' ? null :
                    isNaN(parseInt(formFields.ReadTime.toString())) ? undefined : parseInt(formFields.ReadTime.toString())) : undefined,

            // For JSON fields
            CampaignIds: formFields.CampaignIds !== null ?
                (formFields.CampaignIds.toString().trim() === '' ? [] :
                    JSON.parse(formFields.CampaignIds.toString() || '[]') as number[]) : undefined,
        };

        // Title duplicate check (only if Title is being updated and not blank)
        if (textFields.Title !== undefined && textFields.Title.trim() !== '' && textFields.Title !== existingArticle.Title) {
            const duplicateTitle = await prisma.articleDetails.findFirst({
                where: {
                    Title: textFields.Title,
                    Domain: textFields.Domain || existingArticle.Domain,
                    SubDomain: textFields.SubDomain || existingArticle.SubDomain,
                    Id: { not: textFields.Id }
                }
            });

            if (duplicateTitle) {
                return NextResponse.json(
                    { error: "Article title already exists" },
                    { status: 409 }
                );
            }
        }

        // Handle file upload (only if file is provided)
        const file = formFields.file as File | null;
        let imagePath: string | undefined = undefined;

        if (file) {
            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                const mimeType = file.type;
                const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
                const uniqueId = uuidv4();

                if (!fs.existsSync(UPLOAD_DIR)) {
                    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
                }

                // Process image versions in parallel
                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                // Save all versions
                const baseFileName = `${uniqueId}.${originalExtension}`;
                const versions = [
                    { suffix: '', buffer: originalBuffer },
                    { suffix: '_small', buffer: smallBuffer },
                    { suffix: '_medium', buffer: mediumBuffer }
                ];

                await Promise.all(versions.map(({ suffix, buffer }) => {
                    const fileName = `${uniqueId}${suffix}.${originalExtension}`;
                    const filePath = path.resolve(UPLOAD_DIR, fileName);
                    return fs.promises.writeFile(filePath, buffer);
                }));

                // Delete old image files if they exist
                if (existingArticle.Image) {
                    try {
                        const oldFileName = path.basename(existingArticle.Image);
                        const oldFilePaths = [
                            path.resolve(UPLOAD_DIR, oldFileName),
                            path.resolve(UPLOAD_DIR, oldFileName.replace('.', '_small.')),
                            path.resolve(UPLOAD_DIR, oldFileName.replace('.', '_medium.'))
                        ];

                        await Promise.all(oldFilePaths.map(filePath =>
                            fs.promises.unlink(filePath).catch(() => { })
                        ));
                    } catch (error) {
                        console.error("Error deleting old image files:", error);
                    }
                }

                imagePath = `${baseFileName}`;
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
                console.error("Image processing error:", errorMessage);
                return NextResponse.json(
                    { error: "Failed to process image", details: errorMessage },
                    { status: 500 }
                );
            }
        }

        // Generate URL-friendly name only if Title is being updated and not blank
        const ShowUrlName = (textFields.Title !== undefined && textFields.Title.trim() !== '')
            ? textFields.Title.replace(/[^a-zA-Z0-9]+/g, "-").toLowerCase()
            : undefined;

        // Prepare update data - now properly handles blank values
        const updateData: any = {
            UpdatedAt: new Date(),
        };

        // Add fields to update data only if they're defined (including null values for blanks)
        if (textFields.Title !== undefined) updateData.Title = textFields.Title;
        if (textFields.Category !== undefined) updateData.Category = textFields.Category;
        if (textFields.Url !== undefined) updateData.Url = textFields.Url;
        if (textFields.Description !== undefined) updateData.Description = textFields.Description;
        if (textFields.Published !== undefined) updateData.Published = textFields.Published;
        if (textFields.ShowArticle !== undefined) updateData.ShowArticle = textFields.ShowArticle;
        if (textFields.ShowsAds !== undefined) updateData.ShowsAds = textFields.ShowsAds;
        if (textFields.MetaTitle !== undefined) {
            updateData.MetaTitle = textFields.MetaTitle || textFields.Title || existingArticle.Title;
        }
        if (textFields.MetaDescription !== undefined) {
            updateData.MetaDescription = textFields.MetaDescription || textFields.Description || existingArticle.Description;
        }
        if (textFields.MetaKeys !== undefined) updateData.MetaKeys = textFields.MetaKeys;
        if (textFields.CustomChannal !== undefined) updateData.CustomChannal = textFields.CustomChannal;
        if (textFields.StyleIdLm !== undefined) updateData.StyleIdLm = textFields.StyleIdLm;
        if (textFields.StyleIdDm !== undefined) updateData.StyleIdDm = textFields.StyleIdDm;
        if (textFields.AdRelatedSearches !== undefined) updateData.AdRelatedSearches = textFields.AdRelatedSearches;
        if (textFields.Remark !== undefined) updateData.Remark = textFields.Remark;
        if (textFields.ShortDescription !== undefined) updateData.ShortDescription = textFields.ShortDescription;
        if (textFields.Domain !== undefined) updateData.Domain = textFields.Domain;
        if (textFields.SubDomain !== undefined) updateData.SubDomain = textFields.SubDomain;
        if (textFields.ReadTime !== undefined) updateData.ReadTime = textFields.ReadTime;
        if (textFields.AdReletadSearches2 !== undefined) updateData.AdReletadSearches2 = textFields.AdReletadSearches2;
        if (textFields.CheckRelatedSearches2 !== undefined) updateData.CheckRelatedSearches2 = textFields.CheckRelatedSearches2;
        if (imagePath !== undefined) updateData.Image = imagePath;
        if (ShowUrlName !== undefined) updateData.ShowUrlName = ShowUrlName;

        // Update article
        const updatedArticle = await prisma.articleDetails.update({
            where: { Id: textFields.Id },
            data: updateData
        });

        // Handle campaign mappings only if CampaignIds is provided
        if (textFields.CampaignIds !== undefined) {
            await prisma.articleCampaignMappings.deleteMany({
                where: { ArticleId: textFields.Id }
            });

            const normalizedCampaignIds = Array.isArray(textFields.CampaignIds)
                ? textFields.CampaignIds.filter(id => id != null && !isNaN(Number(id)))
                : [];

            if (normalizedCampaignIds.length > 0) {
                await prisma.articleCampaignMappings.createMany({
                    data: normalizedCampaignIds.map(campaignId => ({
                        ArticleId: textFields.Id,
                        CampaignId: campaignId,
                        CreatedAt: new Date(),
                    })),
                    skipDuplicates: true
                });
            }
        }

        // Fetch the complete updated article with relations
        const articleWithRelations = await prisma.articleDetails.findUnique({
            where: { Id: textFields.Id },
            include: {
                ArticleCampaignMappings: {
                    select: {
                        CampaignId: true
                    }
                }
            }
        });

        // Get campaign mappings count
        const campaignMappingsCount = await prisma.articleCampaignMappings.count({
            where: { ArticleId: textFields.Id }
        });

        return NextResponse.json({
            success: true,
            message: "Article updated successfully"
        }, { status: 200 });

    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error("Error in article update:", errorMessage);
        return NextResponse.json(
            { error: "Failed to update article", details: errorMessage },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}