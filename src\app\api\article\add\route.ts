import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

// Helper function to convert BigInt to string in objects
function serializeBigInt(obj: any): any {
    if (obj === null || obj === undefined) return obj;

    if (typeof obj === 'bigint') {
        return obj.toString();
    }

    if (Array.isArray(obj)) {
        return obj.map(item => serializeBigInt(item));
    }

    if (typeof obj === 'object') {
        const serialized: any = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                serialized[key] = serializeBigInt(obj[key]);
            }
        }
        return serialized;
    }

    return obj;
}

export async function POST(req: NextRequest) {
    type AuthenticatedUser = {
        Id: string;
    };

    const user = await verifyToken(req) as AuthenticatedUser;
    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        const formData = await req.formData();

        // Extract and type cast all form fields
        const textFields = {
            Title: formData.get('Title') as string,
            Category: formData.get('Category') as string,
            Url: formData.get('Url') as string | null,
            Description: formData.get('Description') as string | null,
            Published: formData.get('Published') === 'true',
            ShowArticle: formData.get('ShowArticle') === 'true',
            ShowsAds: formData.get('ShowsAds') === 'true',
            MetaTitle: formData.get('MetaTitle') as string | null,
            MetaDescription: formData.get('MetaDescription') as string | null,
            MetaKeys: formData.get('MetaKeys') as string || '',
            CustomChannal: formData.get('CustomChannal') as string | null,
            StyleIdLm: formData.get('StyleIdLm') as string | null,
            StyleIdDm: formData.get('StyleIdDm') as string | null,
            AdRelatedSearches: formData.get('AdRelatedSearches') as string | null,
            Remark: formData.get('Remark') as string || '',
            ShortDescription: formData.get('ShortDescription') as string | null,
            Domain: formData.get('Domain') as string,
            SubDomain: formData.get('SubDomain') as string | null,
            CampaignIds: JSON.parse(formData.get('CampaignIds') as string || '[]') as number[],
            User_Id_Settings: user.Id,
            ReadTime: formData.get('ReadTime') ? parseInt(formData.get('ReadTime') as string) : null,
            CheckRelatedSearches2: formData.get('CheckRelatedSearches2') === 'true',
            AdReletadSearches2: formData.get('AdReletadSearches2') as string | null
        };

        // UUID validation function
        const isValidUUID = (uuid: string): boolean => {
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            return uuidRegex.test(uuid);
        };

        // Enhanced validation with detailed error messages
        const validationErrors: string[] = [];

        if (!textFields.Title || textFields.Title.trim() === '') {
            validationErrors.push('Title is required and cannot be empty');
        }

        if (!textFields.Category || textFields.Category === '') {
            validationErrors.push('Category is required and cannot be empty');
        } else if (!isValidUUID(textFields.Category)) {
            validationErrors.push('Category must be a valid UUID');
        }

        if (!textFields.Domain || textFields.Domain.trim() === '') {
            validationErrors.push('Domain is required and cannot be empty');
        } else if (!isValidUUID(textFields.Domain)) {
            validationErrors.push('Domain must be a valid UUID');
        }

        // Validate optional UUID fields
        if (textFields.SubDomain && !isValidUUID(textFields.SubDomain)) {
            validationErrors.push('SubDomain must be a valid UUID');
        }

        if (textFields.CustomChannal && !isValidUUID(textFields.CustomChannal)) {
            validationErrors.push('CustomChannal must be a valid UUID');
        }

        if (textFields.StyleIdLm && !isValidUUID(textFields.StyleIdLm)) {
            validationErrors.push('StyleId must be a valid UUID');
        }

        if (textFields.StyleIdLm && !isValidUUID(textFields.StyleIdLm)) {
            validationErrors.push('StyleId must be a valid UUID');
        }

        if (textFields.Title && textFields.Title.length > 255) {
            validationErrors.push('Title must be less than 255 characters');
        }

        if (textFields.MetaTitle && textFields.MetaTitle.length > 255) {
            validationErrors.push('MetaTitle must be less than 255 characters');
        }

        if (textFields.MetaDescription && textFields.MetaDescription.length > 500) {
            validationErrors.push('MetaDescription must be less than 500 characters');
        }

        if (validationErrors.length > 0) {
            return NextResponse.json(
                { error: "Validation failed", details: validationErrors },
                { status: 400 }
            );
        }

        // Check for existing article
        const existing = await prisma.articleDetails.findFirst({
            where: {
                Title: textFields.Title,
                Domain: textFields.Domain,
                SubDomain: textFields.SubDomain
            }
        });

        if (existing) {
            return NextResponse.json(
                { error: "Article title already exists for this domain" },
                { status: 409 }
            );
        }

        // Handle file upload
        const file = formData.get('file') as File | null;
        let imagePath: string | null = null;

        if (file) {
            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                const mimeType = file.type;
                const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
                const uniqueId = uuidv4();

                if (!fs.existsSync(UPLOAD_DIR)) {
                    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
                }

                // Process image versions in parallel
                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                // Save all versions
                const baseFileName = `${uniqueId}.${originalExtension}`;
                const versions = [
                    { suffix: '', buffer: originalBuffer },
                    { suffix: '_small', buffer: smallBuffer },
                    { suffix: '_medium', buffer: mediumBuffer }
                ];

                await Promise.all(versions.map(({ suffix, buffer }) => {
                    const fileName = `${uniqueId}${suffix}.${originalExtension}`;
                    const filePath = path.resolve(UPLOAD_DIR, fileName);
                    return fs.promises.writeFile(filePath, buffer);
                }));

                imagePath = `${baseFileName}`;
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
                console.error("Image processing error:", errorMessage);
                return NextResponse.json(
                    { error: "Failed to process image", details: errorMessage },
                    { status: 500 }
                );
            }
        }

        // Generate URL-friendly name
        const ShowUrlName = textFields.Title
            .replace(/[^a-zA-Z0-9]+/g, "-")
            .toLowerCase()
            .replace(/^-+|-+$/g, ''); // Remove leading/trailing dashes

        // Normalize campaign IDs
        const normalizedCampaignIds = Array.isArray(textFields.CampaignIds)
            ? textFields.CampaignIds.filter(id => id != null && !isNaN(Number(id)))
            : [];

        // Create article data object with proper Prisma relations
        const articleData: any = {
            Title: textFields.Title.trim(),
            Url: textFields.Url || null,
            Description: textFields.Description || null,
            Published: textFields.Published,
            ShowArticle: textFields.ShowArticle,
            ShowsAds: textFields.ShowsAds,
            MetaTitle: textFields.MetaTitle || textFields.Title.trim(),
            MetaDescription: textFields.MetaDescription || textFields.Description || null,
            MetaKeys: textFields.MetaKeys.trim(),
            AdRelatedSearches: textFields.AdRelatedSearches || null,
            Remark: textFields.Remark.trim(),
            Image: imagePath,
            ShowUrlName,
            ShortDescription: textFields.ShortDescription || null,
            ReadTime: textFields.ReadTime,
            AdReletadSearches2: textFields.AdReletadSearches2 || null,
            CheckRelatedSearches2: textFields.CheckRelatedSearches2,
            CreatedAt: new Date(),
            UpdatedAt: new Date(),

            // Connect related models using Prisma's connect syntax
            Category_ArticleDetails_CategoryToCategory: {
                connect: { Id: textFields.Category }
            },
            Domain_ArticleDetails_DomainToDomain: {
                connect: { Id: textFields.Domain }
            },
            AdminUser: {
                connect: { Id: textFields.User_Id_Settings }
            }
        };

        // Add optional relations only if they exist and are valid
        if (textFields.SubDomain && isValidUUID(textFields.SubDomain)) {
            articleData.SubDomain_ArticleDetails_SubDomainToSubDomain = {
                connect: { Id: textFields.SubDomain }
            };
        }

        if (textFields.CustomChannal && isValidUUID(textFields.CustomChannal)) {
            articleData.Channals = {
                connect: { Id: textFields.CustomChannal }
            };
        }

        if (textFields.StyleIdLm && isValidUUID(textFields.StyleIdLm)) {
            articleData.StyleIds = {
                connect: { Id: textFields.StyleIdLm }
            };
        }
        if (textFields.StyleIdDm && isValidUUID(textFields.StyleIdDm)) {
            articleData.StyleIds = {
                connect: { Id: textFields.StyleIdDm }
            };
        }

        // Create the article with proper relations
        const newArticle = await prisma.articleDetails.create({
            data: articleData
        });

        // Create campaign mappings if any campaign IDs provided
        if (normalizedCampaignIds.length > 0) {
            try {
                await prisma.articleCampaignMappings.createMany({
                    data: normalizedCampaignIds.map(campaignId => ({
                        ArticleId: newArticle.Id,
                        CampaignId: campaignId,
                        CreatedAt: new Date(),
                    })),
                    skipDuplicates: true
                });
            } catch (campaignError) {
                console.error("Error creating campaign mappings:", campaignError);
                // Continue without failing the entire request
            }
        }

        // Fetch the complete article with relations
        const articleWithRelations = await prisma.articleDetails.findUnique({
            where: { Id: newArticle.Id },
            include: {
                ArticleCampaignMappings: {
                    select: {
                        CampaignId: true
                    }
                },
                Category_ArticleDetails_CategoryToCategory: true,
                Domain_ArticleDetails_DomainToDomain: true,
                AdminUser: {
                    select: {
                        Id: true
                    }
                }
            }
        });

        // Serialize the response to handle BigInt values
        const serializedArticle = serializeBigInt(articleWithRelations);

        return NextResponse.json({
            success: true,
            message: "Article created successfully",
            article: serializedArticle,
            campaignMappings: normalizedCampaignIds.length
        }, { status: 201 });

    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error("Error in article creation:", errorMessage);
        console.error("Full error:", error);

        return NextResponse.json(
            {
                error: "Failed to create article",
                details: errorMessage,
                stack: error instanceof Error ? error.stack : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}

// Helper function to validate and sanitize data (optional)
function sanitizeStringField(value: string | null, maxLength?: number): string | null {
    if (!value) return null;
    const trimmed = value.trim();
    if (trimmed === '') return null;
    if (maxLength && trimmed.length > maxLength) {
        return trimmed.substring(0, maxLength);
    }
    return trimmed;
}