// import { prisma } from '../../../../lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
// import { google } from "googleapis";
// import {
//     generateReport,
//     getAdClients,
//     getAdSenseAccounts,
// } from '@/utils/functions';

// const getDaysAgo = (days: number) => {
//     const date = new Date();
//     date.setDate(date.getDate() - days);
//     return date.toISOString().split('T')[0];
// };

// const filterDataByBreakpoints = (data: any[], breakpoints: any) => {
//     return data.map(item => {
//         const filteredItem: any = {};

//         filteredItem.estimatedEarnings = item.estimatedEarnings || 0;
//         filteredItem.impressions = item.impressions || 0;
//         filteredItem.impressionsRpm = item.impressionsRpm || 0;
//         filteredItem.clicks = item.clicks || 0;
//         filteredItem.impressionsCtr = item.impressionsCtr || 0;
//         filteredItem.costPerClick = item.costPerClick || 0;

//         if (breakpoints.country === true) {
//             filteredItem.country = item.country || '';
//         }

//         if (breakpoints.platform === true) {
//             filteredItem.platformType = item.platformType || '';
//         }

//         if (breakpoints.date === true) {
//             filteredItem.date = item.date || '';
//         }

//         if (breakpoints.customChannel === true) {
//             filteredItem.customChannelId = item.customChannelId || '';
//             if (item.customChannelName) {
//                 filteredItem.customChannelName = item.customChannelName;
//             }
//         }

//         if (breakpoints.styleId === true) {
//             filteredItem.customSearchStyleId = item.customSearchStyleId || '';
//             if (item.customSearchStyleName) {
//                 filteredItem.customSearchStyleName = item.customSearchStyleName;
//             }
//         }

//         return filteredItem;
//     });
// };

// // FIXED: Improved validation to handle empty arrays properly
// const validateSelections = async (selectedChannels: string[], selectedStyles: string[]) => {
//     const validChannelIds = [];
//     const channelDimensions = [];

//     // Only validate if channels are actually selected
//     if (selectedChannels && selectedChannels.length > 0) {
//         for (const channelId of selectedChannels) {
//             const channel = await prisma.channals.findUnique({ where: { Id: channelId } });
//             if (channel && channel.ReportingDimensionId) {
//                 validChannelIds.push(channelId);
//                 channelDimensions.push(channel.ReportingDimensionId);
//             }
//         }
//     }

//     const validStyleIds = [];
//     const styleDimensions = [];

//     // Only validate if styles are actually selected
//     if (selectedStyles && selectedStyles.length > 0) {
//         for (const styleId of selectedStyles) {
//             const style = await prisma.styleIds.findUnique({ where: { Id: styleId } });
//             if (style && style.StyleId) {
//                 validStyleIds.push(styleId);
//                 styleDimensions.push(style.StyleId);
//             }
//         }
//     }

//     return {
//         validChannelIds,
//         validStyleIds,
//         channelDimensions,
//         styleDimensions,
//         hasValidChannels: validChannelIds.length > 0,
//         hasValidStyles: validStyleIds.length > 0
//     };
// };

// // FIXED: Major improvements to database query logic
// const getDataFromDB = async (startDate: string, endDate: string, selectedChannels: string[], selectedStyles: string[]) => {
//     try {
//         const validation = await validateSelections(selectedChannels, selectedStyles);

//         const whereClause: any = {
//             Date: {
//                 gte: new Date(startDate + 'T00:00:00.000Z'), // FIXED: Proper date handling
//                 lte: new Date(endDate + 'T23:59:59.999Z')   // FIXED: Include end date completely
//             }
//         };

//         // FIXED: Improved OR logic instead of AND for channels and styles
//         const orConditions = [];

//         // If channels are selected, add channel filter
//         if (selectedChannels.length > 0 && validation.channelDimensions.length > 0) {
//             orConditions.push({
//                 ChannalId: { in: validation.channelDimensions }
//             });
//         }

//         // If styles are selected, add style filter
//         if (selectedStyles.length > 0 && validation.styleDimensions.length > 0) {
//             orConditions.push({
//                 StyleId: { in: validation.styleDimensions }
//             });
//         }

//         // FIXED: Use OR instead of AND when both channels and styles are selected
//         if (orConditions.length > 0) {
//             if (selectedChannels.length > 0 && selectedStyles.length > 0) {
//                 // If both are selected, use OR to get records matching either
//                 whereClause.OR = orConditions;
//             } else {
//                 // If only one type is selected, use the single condition
//                 whereClause.AND = orConditions;
//             }
//         }

//         // FIXED: Don't return empty array prematurely - let the query run
//         console.log('Database query where clause:', JSON.stringify(whereClause, null, 2));

//         const dbData = await prisma.revenue.findMany({
//             where: whereClause,
//             orderBy: { Date: 'asc' }
//         });

//         console.log(`Database returned ${dbData.length} records`);
//         return dbData;
//     } catch (error) {
//         console.error('Database query error:', error);
//         return [];
//     }
// };

// const convertDBDataToJSON = async (dbData: any[]) => {
//     const jsonData = [];

//     for (const row of dbData) {
//         const customChannelId = row.ChannalId || '';
//         const customSearchStyleId = row.StyleId || '';

//         jsonData.push({
//             country: row.Country || '',
//             platformType: row.PlatfromType || '',
//             date: row.Date.toISOString().split('T')[0],
//             customChannelId: customChannelId,
//             customSearchStyleId: customSearchStyleId,
//             estimatedEarnings: parseFloat(row.EstimatedEarnings) || 0,
//             impressions: parseInt(row.Impressions) || 0,
//             impressionsRpm: parseFloat(row.ImpressionsRpm) || 0,
//             clicks: parseInt(row.Clicks) || 0,
//             impressionsCtr: parseFloat(row.ImpressionsCtr) || 0,
//             costPerClick: parseFloat(row.CostPerClick) || 0
//         });
//     }

//     return jsonData;
// };

// const convertCSVToJSON = async (csvString: string) => {
//     if (!csvString || csvString.trim() === '') {
//         return [];
//     }

//     const lines = csvString.split('\n').filter(line => line.trim() !== '');
//     if (lines.length <= 1) {
//         return [];
//     }

//     const headers = lines[0].split(',').map(h => h.trim());
//     const jsonData = [];

//     for (let i = 1; i < lines.length; i++) {
//         const line = lines[i].trim();
//         if (!line) continue;

//         const values = [];
//         let current = '';
//         let inQuotes = false;

//         for (let j = 0; j < line.length; j++) {
//             const char = line[j];
//             if (char === '"') {
//                 inQuotes = !inQuotes;
//             } else if (char === ',' && !inQuotes) {
//                 values.push(current.trim());
//                 current = '';
//             } else {
//                 current += char;
//             }
//         }
//         values.push(current.trim());

//         const cleanValues = values.map(val => val.replace(/^"|"$/g, '').trim());

//         if (cleanValues.length < 8) {
//             continue;
//         }

//         const row = {
//             country: cleanValues[0] || '',
//             platformType: cleanValues[1] || '',
//             date: cleanValues[2] || '',
//             customChannelName: cleanValues[3] || '',
//             customChannelId: cleanValues[4] || '',
//             customSearchStyleName: cleanValues[5] || '',
//             customSearchStyleId: cleanValues[6] || '',
//             estimatedEarnings: parseFloat(cleanValues[7]) || 0,
//             impressions: parseInt(cleanValues[8]) || 0,
//             impressionsRpm: parseFloat(cleanValues[9]) || 0,
//             clicks: parseInt(cleanValues[10]) || 0,
//             impressionsCtr: parseFloat(cleanValues[11]) || 0,
//             costPerClick: parseFloat(cleanValues[12]) || 0
//         };

//         jsonData.push(row);
//     }

//     return jsonData;
// };

// async function upsertReportData(combinedReport: string) {
//     if (!combinedReport || combinedReport.trim() === '') {
//         return;
//     }

//     try {
//         const lines = combinedReport.split('\n').filter(line => line.trim() !== '');

//         if (lines.length <= 1) {
//             return;
//         }

//         const headers = lines[0].split(',').map(h => h.trim());
//         const rows = lines.slice(1);
//         const chunkSize = 25;
//         let totalProcessed = 0;
//         let totalErrors = 0;

//         for (let i = 0; i < rows.length; i += chunkSize) {
//             const chunk = rows.slice(i, i + chunkSize);
//             const batchUpserts = [];

//             for (const row of chunk) {
//                 if (!row.trim()) continue;

//                 try {
//                     const values = [];
//                     let current = '';
//                     let inQuotes = false;

//                     for (let j = 0; j < row.length; j++) {
//                         const char = row[j];
//                         if (char === '"') {
//                             inQuotes = !inQuotes;
//                         } else if (char === ',' && !inQuotes) {
//                             values.push(current.trim());
//                             current = '';
//                         } else {
//                             current += char;
//                         }
//                     }
//                     values.push(current.trim());

//                     const cleanColumns = values.map(col => col.replace(/^"|"$/g, '').trim());

//                     if (cleanColumns.length < 8) {
//                         continue;
//                     }

//                     const [
//                         countryName,
//                         platformTypeName,
//                         date,
//                         customChannelName,
//                         customChannelId,
//                         customSearchStyleName,
//                         customSearchStyleId,
//                         estimatedEarnings,
//                         impressions,
//                         impressionsRpm,
//                         clicks,
//                         impressionsCtr,
//                         costPerClick
//                     ] = cleanColumns;

//                     const formattedDate = date ? new Date(date) : null;
//                     if (!formattedDate || isNaN(formattedDate.getTime())) {
//                         continue;
//                     }

//                     const processValue = (value: string) => value?.toString().trim() || "";
//                     const processNumericValue = (value: string) => {
//                         if (!value || value.trim() === '') return 0;
//                         const num = parseFloat(value.toString());
//                         return isNaN(num) ? 0 : num;
//                     };

//                     const finalChannelId = processValue(customChannelId);
//                     const finalStyleId = processValue(customSearchStyleId);
//                     const finalCountry = processValue(countryName);
//                     const finalPlatformType = processValue(platformTypeName);

//                     const uniqueIdentifier = {
//                         Date: formattedDate,
//                         StyleId: finalStyleId,
//                         ChannalId: finalChannelId,
//                         Country: finalCountry,
//                         PlatfromType: finalPlatformType
//                     };

//                     const revenueData = {
//                         EstimatedEarnings: processNumericValue(estimatedEarnings),
//                         Impressions: Math.floor(processNumericValue(impressions)),
//                         ImpressionsRpm: processNumericValue(impressionsRpm),
//                         Clicks: Math.floor(processNumericValue(clicks)),
//                         ImpressionsCtr: processNumericValue(impressionsCtr),
//                         CostPerClick: processNumericValue(costPerClick)
//                     };

//                     batchUpserts.push(
//                         prisma.revenue.upsert({
//                             where: {
//                                 Date_StyleId_ChannalId_Country_PlatfromType: uniqueIdentifier
//                             },
//                             create: {
//                                 ...uniqueIdentifier,
//                                 ...revenueData
//                             },
//                             update: revenueData
//                         })
//                     );

//                 } catch (parseError) {
//                     totalErrors++;
//                     continue;
//                 }
//             }

//             if (batchUpserts.length > 0) {
//                 try {
//                     const results = await Promise.all(batchUpserts);
//                     totalProcessed += batchUpserts.length;
//                 } catch (batchError) {
//                     totalErrors += batchUpserts.length;

//                     for (const upsertOperation of batchUpserts) {
//                         try {
//                             await upsertOperation;
//                             totalProcessed++;
//                         } catch (individualError) {
//                             totalErrors++;
//                         }
//                     }
//                 }
//             }

//             if (i + chunkSize < rows.length) {
//                 await new Promise(resolve => setTimeout(resolve, 100));
//             }
//         }

//         console.log(`Upsert completed: ${totalProcessed} processed, ${totalErrors} errors`);

//     } catch (error) {
//         console.error('Upsert error:', error);
//         throw error;
//     }
// }

// export async function POST(req: NextRequest) {
//     try {
//         const user = await verifyToken(req);
//         if (!user?.Id) {
//             return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         }

//         const { searchParams } = new URL(req.url);
//         const code = searchParams.get("code");
//         const startDateParams = searchParams.get("startDate");
//         const endDateParams = searchParams.get("endDate");
//         const breakPoints = searchParams.get("breakPoints") || "{}";
//         const toggle = searchParams.get("toggle") || "false";

//         let parsedBreakpoints;
//         try {
//             parsedBreakpoints = JSON.parse(breakPoints);
//         } catch (error) {
//             parsedBreakpoints = {};
//         }

//         const body = await req.json();
//         let selectedChannels = body.selectedChannels || [];
//         let selectedStyles = body.selectedStyles || [];

//         // FIXED: Add logging to debug selection issues
//         console.log('Selected channels:', selectedChannels);
//         console.log('Selected styles:', selectedStyles);

//         const userSettings = await prisma.adminUserSetting.findFirst({});
//         if (!userSettings?.RevenueClientId || !userSettings?.RevenueClientSecret) {
//             return NextResponse.json(
//                 { message: 'OAuth credentials not found in database' },
//                 { status: 404 }
//             );
//         }

//         const oauth2Client = new google.auth.OAuth2(
//             userSettings.RevenueClientId,
//             userSettings.RevenueClientSecret,
//             process.env.REDIRECT_URI
//         );

//         const authUrl = oauth2Client.generateAuthUrl({
//             access_type: 'offline',
//             scope: [
//                 'https://www.googleapis.com/auth/adsense.readonly',
//                 'https://www.googleapis.com/auth/adsense',
//             ],
//             prompt: 'consent',
//             include_granted_scopes: true,
//         });

//         let tokens = null;
//         if (userSettings.RevenueAccessToken && userSettings.RevenueRefreshToken) {
//             tokens = {
//                 access_token: userSettings.RevenueAccessToken,
//                 refresh_token: userSettings.RevenueRefreshToken,
//             };
//         }

//         if (!code && !tokens) {
//             return NextResponse.redirect(authUrl);
//         }

//         if (code) {
//             try {
//                 const { tokens: newTokens } = await oauth2Client.getToken(code);
//                 oauth2Client.setCredentials(newTokens);
//                 tokens = newTokens;

//                 await prisma.adminUserSetting.updateMany({
//                     data: {
//                         RevenueAccessToken: newTokens.access_token,
//                         RevenueRefreshToken: newTokens.refresh_token,
//                     },
//                 });

//                 return NextResponse.json(
//                     { message: 'Authorization successful', tokens: newTokens },
//                     { status: 200 }
//                 );
//             } catch (error) {
//                 const errorMessage = error instanceof Error ? error.message : 'Unknown error during token exchange';
//                 return NextResponse.json(
//                     { error: 'Failed to exchange authorization code for tokens', details: errorMessage },
//                     { status: 500 }
//                 );
//             }
//         }

//         if (tokens) {
//             oauth2Client.setCredentials(tokens);

//             try {
//                 const adsense = google.adsense({ version: 'v2', auth: oauth2Client });
//                 await adsense.accounts.list({ pageSize: 1 });
//             } catch (error) {
//                 const isAuthError =
//                     error instanceof Error &&
//                     (error.message.includes('invalid_token') ||
//                         (typeof (error as any).code === 'number' && (error as any).code === 401));

//                 if (isAuthError) {
//                     try {
//                         const { credentials } = await oauth2Client.refreshAccessToken();
//                         tokens = {
//                             access_token: credentials.access_token,
//                             refresh_token: credentials.refresh_token || userSettings.RevenueRefreshToken,
//                         };

//                         await prisma.adminUserSetting.updateMany({
//                             data: {
//                                 RevenueAccessToken: credentials.access_token,
//                                 RevenueRefreshToken: credentials.refresh_token || userSettings.RevenueRefreshToken,
//                             },
//                         });

//                         oauth2Client.setCredentials(tokens);
//                     } catch (refreshError) {
//                         return NextResponse.redirect(authUrl);
//                     }
//                 } else {
//                     throw error;
//                 }
//             }
//         }

//         const fetchChannelsRecord = async (Id: string) => {
//             const record = await prisma.channals.findUnique({ where: { Id: Id } });
//             return userSettings?.PubId
//                 ? `partner-pub-${userSettings.PubId}:${record?.ReportingDimensionId?.split(":")[1]}`
//                 : record?.ReportingDimensionId;
//         };

//         const fetchStyleDimensionId = async (Id: string) => {
//             const record = await prisma.styleIds.findUnique({ where: { Id: Id } });
//             return record?.StyleId;
//         };

//         // FIXED: Better handling of empty selections
//         if (!selectedChannels || selectedChannels.length === 0) {
//             const allChannels = await prisma.channals.findMany({
//                 select: { Id: true }
//             });
//             selectedChannels = allChannels.map(channel => channel.Id);
//             console.log('Using all channels:', selectedChannels.length);
//         }

//         if (!selectedStyles || selectedStyles.length === 0) {
//             const allStyles = await prisma.styleIds.findMany({
//                 select: { Id: true }
//             });
//             selectedStyles = allStyles.map(style => style.Id);
//             console.log('Using all styles:', selectedStyles.length);
//         }

//         const today = new Date();
//         const startDate = startDateParams ? new Date(startDateParams) : new Date();
//         const endDate = endDateParams ? new Date(endDateParams) : new Date();

//         const isToday = startDate.toDateString() === today.toDateString() && endDate.toDateString() === today.toDateString();

//         if (isToday) {
//             const finalStartDate = {
//                 day: today.getDate(),
//                 month: today.getMonth() + 1,
//                 year: today.getFullYear(),
//             };
//             const finalEndDate = finalStartDate;

//             const metrics = [
//                 "ESTIMATED_EARNINGS",
//                 "IMPRESSIONS",
//                 "IMPRESSIONS_RPM",
//                 "CLICKS",
//                 "IMPRESSIONS_CTR",
//                 "COST_PER_CLICK",
//             ];

//             let allReportsData: string[] = [];

//             if (selectedChannels.length > 0) {
//                 const channelDimensions = await Promise.all(
//                     selectedChannels.map(fetchChannelsRecord)
//                 );
//                 const validChannels = channelDimensions.filter(Boolean);

//                 if (validChannels.length > 0) {
//                     const channelReport = await generateReport(
//                         oauth2Client,
//                         userSettings.AdsAccountId,
//                         google,
//                         finalStartDate,
//                         finalEndDate,
//                         metrics,
//                         parsedBreakpoints,
//                         validChannels,
//                         toggle
//                     );

//                     if (typeof channelReport === 'string' && channelReport.trim()) {
//                         const lines = channelReport.split('\n');
//                         if (lines.length > 1) {
//                             const [currentHeader, ...rows] = lines;
//                             const filteredRows = rows.filter(r => r.trim() !== '');
//                             allReportsData.push(...filteredRows);
//                         }
//                     }
//                 }
//             }

//             if (selectedStyles.length > 0) {
//                 const styleDimensions = await Promise.all(
//                     selectedStyles.map(fetchStyleDimensionId)
//                 );
//                 const validStyles = styleDimensions.filter(Boolean);

//                 if (validStyles.length > 0) {
//                     const styleReport = await generateReport(
//                         oauth2Client,
//                         userSettings.AdsAccountId,
//                         google,
//                         finalStartDate,
//                         finalEndDate,
//                         metrics,
//                         parsedBreakpoints,
//                         validStyles,
//                         toggle
//                     );

//                     if (typeof styleReport === 'string' && styleReport.trim()) {
//                         const lines = styleReport.split('\n');
//                         if (lines.length > 1) {
//                             const [currentHeader, ...rows] = lines;
//                             const filteredRows = rows.filter(r => r.trim() !== '');
//                             allReportsData.push(...filteredRows);
//                         }
//                     }
//                 }
//             }

//             if (allReportsData.length > 0) {
//                 const csvHeader = 'Country,Platform Type,Date,Custom Channel Name,Custom Channel Id,Custom Search Style Name,Custom Search Style Id,Estimated Earnings,Impressions,Impressions RPM,Clicks,Impressions CTR,Cost Per Click';
//                 const csvString = [csvHeader, ...allReportsData].join('\n');

//                 const jsonData = await convertCSVToJSON(csvString);

//                 if (jsonData.length > 0) {
//                     await upsertReportData(csvString);

//                     const dbData = await getDataFromDB(
//                         startDateParams || startDate.toISOString().split('T')[0],
//                         endDateParams || endDate.toISOString().split('T')[0],
//                         selectedChannels,
//                         selectedStyles
//                     );

//                     if (dbData.length > 0) {
//                         const dbJsonData = await convertDBDataToJSON(dbData);
//                         const filteredData = filterDataByBreakpoints(dbJsonData, parsedBreakpoints);

//                         return NextResponse.json({
//                             success: true,
//                             data: filteredData,
//                             breakpoints: parsedBreakpoints,
//                             source: 'database_after_api_upsert',
//                             totalRecords: filteredData.length // FIXED: Add count for debugging
//                         }, { status: 200 });
//                     } else {
//                         const filteredData = filterDataByBreakpoints(jsonData, parsedBreakpoints);

//                         return NextResponse.json({
//                             success: true,
//                             data: filteredData,
//                             breakpoints: parsedBreakpoints,
//                             source: 'api_fallback',
//                             totalRecords: filteredData.length // FIXED: Add count for debugging
//                         }, { status: 200 });
//                     }
//                 } else {
//                     return NextResponse.json({
//                         message: "Failed to process API data",
//                         dateRange: { startDate: startDateParams, endDate: endDateParams },
//                         selectedChannels: selectedChannels,
//                         selectedStyles: selectedStyles,
//                         data: [],
//                         breakpoints: parsedBreakpoints
//                     }, { status: 404 });
//                 }
//             } else {
//                 return NextResponse.json({
//                     message: "No revenue data found for today",
//                     dateRange: { startDate: startDateParams, endDate: endDateParams },
//                     selectedChannels: selectedChannels,
//                     selectedStyles: selectedStyles,
//                     data: [],
//                     breakpoints: parsedBreakpoints
//                 }, { status: 404 });
//             }
//         } else {
//             const dbData = await getDataFromDB(
//                 startDateParams || startDate.toISOString().split('T')[0],
//                 endDateParams || endDate.toISOString().split('T')[0],
//                 selectedChannels,
//                 selectedStyles
//             );

//             console.log(`Final database query returned ${dbData.length} records`);

//             if (dbData.length > 0) {
//                 const jsonData = await convertDBDataToJSON(dbData);
//                 const filteredData = filterDataByBreakpoints(jsonData, parsedBreakpoints);

//                 return NextResponse.json({
//                     success: true,
//                     data: filteredData,
//                     breakpoints: parsedBreakpoints,
//                     source: 'database',
//                     totalRecords: filteredData.length, // FIXED: Add count for debugging
//                     rawDbCount: dbData.length // FIXED: Show raw DB count vs filtered count
//                 }, { status: 200 });
//             } else {
//                 return NextResponse.json({
//                     message: "No revenue data found for the specified date range",
//                     dateRange: { startDate: startDateParams, endDate: endDateParams },
//                     selectedChannels: selectedChannels,
//                     selectedStyles: selectedStyles,
//                     data: [],
//                     source: 'database',
//                     breakpoints: parsedBreakpoints
//                 }, { status: 404 });
//             }
//         }

//     } catch (error) {
//         console.error('API Error:', error);
//         return NextResponse.json({
//             error: "Internal server error",
//             details: error instanceof Error ? error.message : 'Unknown error'
//         }, { status: 500 });
//     } finally {
//         await prisma.$disconnect();
//     }
// }   





import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { google } from "googleapis";
import {
    generateReport,
    getAdClients,
    getAdSenseAccounts,
} from '@/utils/functions';

type AuthenticatedUser = {
    User_Type: string;
    Id: string;
};

type RevenueData = {
    country: string;
    platformType: string;
    date: string;
    customChannelId?: string;
    customChannelName?: string;
    customSearchStyleId?: string;
    customSearchStyleName?: string;
    customChannel?: string;
    customSearchStyle?: string;
    estimatedEarnings: number;
    impressions: number;
    impressionsRpm: number;
    clicks: number;
    impressionsCtr: number;
    costPerClick: number;
    _ctrSum?: number;
    _itemCount?: number;
};

type Breakpoints = {
    country?: boolean;
    platform?: boolean;
    date?: boolean;
    customChannel?: boolean;
    styleId?: boolean;
};

type GroupedData = {
    [key: string]: RevenueData;
};

type ValidationResult = {
    validChannelIds: string[];
    validStyleIds: string[];
    channelDimensions: string[];
    styleDimensions: string[];
    hasValidChannels: boolean;
    hasValidStyles: boolean;
};

const getDaysAgo = (days: number): string => {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date.toISOString().split('T')[0];
};

const filterDataByBreakpoints = (
    data: RevenueData[],
    breakpoints: Breakpoints,
    selectedChannels: string[],
    selectedStyles: string[],
    search: string = '',
    orderBy: string = "date",
    orderDir: string = "asc",
    start: number = 0,
    length: number = -1
): { data: RevenueData[]; total: number; filtered: number } => {
    if (!breakpoints || Object.keys(breakpoints).length === 0) {
        let filteredData = data;
        if (search) {
            const searchLower = search.toLowerCase();
            filteredData = data.filter(item =>
                (item.country?.toLowerCase().includes(searchLower)) ||
                (item.platformType?.toLowerCase().includes(searchLower)) ||
                (item.date?.toLowerCase().includes(searchLower)) ||
                (item.customChannelName?.toLowerCase().includes(searchLower)) ||
                (item.customSearchStyleName?.toLowerCase().includes(searchLower)) ||
                (item.estimatedEarnings?.toString().includes(search)) ||
                (item.impressions?.toString().includes(search)) ||
                (item.clicks?.toString().includes(search))
            );
        }

        filteredData.sort((a, b) => {
            const aValue = a[orderBy as keyof RevenueData] || '';
            const bValue = b[orderBy as keyof RevenueData] || '';

            if (orderDir === 'asc') {
                return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
            } else {
                return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
            }
        });

        const total = filteredData.length;
        const paginatedData = length === -1
            ? filteredData
            : filteredData.slice(start, start + length);

        return {
            data: paginatedData,
            total: data.length,
            filtered: total
        };
    }

    const groupAndTotal = (data: RevenueData[], groupByProps: string[]): RevenueData[] => {
        const grouped: {
            [key: string]: RevenueData & { _ctrSum: number; _itemCount: number };
        } = {};

        data.forEach(item => {
            const keyParts = [];

            if (breakpoints.country) keyParts.push(item.country || '');
            if (breakpoints.platform) keyParts.push(item.platformType || '');
            if (breakpoints.date) keyParts.push(item.date || '');
            if (breakpoints.customChannel) keyParts.push(item.customChannelId || '');
            if (breakpoints.styleId) keyParts.push(item.customSearchStyleId || '');

            const key = keyParts.join('|');

            if (!grouped[key]) {
                const baseObject: any = {
                    estimatedEarnings: 0,
                    impressions: 0,
                    impressionsRpm: 0,
                    clicks: 0,
                    impressionsCtr: 0,
                    costPerClick: 0,
                    _ctrSum: 0,
                    _itemCount: 0
                };

                if (breakpoints.country) baseObject.country = item.country;
                if (breakpoints.platform) baseObject.platformType = item.platformType;
                if (breakpoints.date) baseObject.date = item.date;
                if (breakpoints.customChannel) {
                    baseObject.customChannelId = item.customChannelId;
                    baseObject.customChannelName = item.customChannelName;
                    baseObject.customChannel = item.customChannel;
                }
                if (breakpoints.styleId) {
                    baseObject.customSearchStyleId = item.customSearchStyleId;
                    baseObject.customSearchStyleName = item.customSearchStyleName;
                    baseObject.customSearchStyle = item.customSearchStyle;
                }

                grouped[key] = baseObject;
            }

            const group = grouped[key];
            group.estimatedEarnings += item.estimatedEarnings;
            group.impressions += item.impressions;
            group.clicks += item.clicks;

            group._ctrSum += item.impressionsCtr || 0;
            group._itemCount += 1;

            group.impressionsRpm = group.impressions > 0
                ? (group.estimatedEarnings * 1000) / group.impressions
                : 0;

            group.impressionsCtr = group._itemCount > 0
                ? (group._ctrSum / group._itemCount)
                : 0;

            group.costPerClick = group.clicks > 0
                ? group.estimatedEarnings / group.clicks
                : 0;
        });

        return Object.values(grouped).map(({ _ctrSum, _itemCount, customChannelId, customChannelName, customSearchStyleId, customSearchStyleName, ...rest }) => rest);
    };

    const groupByProps: string[] = [];
    if (breakpoints.country) groupByProps.push('country');
    if (breakpoints.platform) groupByProps.push('platformType');
    if (breakpoints.date) groupByProps.push('date');
    if (breakpoints.customChannel) groupByProps.push('customChannelId');
    if (breakpoints.styleId) groupByProps.push('customSearchStyleId');

    const groupedData = groupAndTotal(data, groupByProps);

    let filteredData = groupedData;
    if (search) {
        const searchLower = search.toLowerCase();
        filteredData = groupedData.filter(item =>
            (item.country?.toLowerCase().includes(searchLower)) ||
            (item.platformType?.toLowerCase().includes(searchLower)) ||
            (item.date?.toLowerCase().includes(searchLower)) ||
            (item.customChannelName?.toLowerCase().includes(searchLower)) ||
            (item.customSearchStyleName?.toLowerCase().includes(searchLower)) ||
            (item.estimatedEarnings?.toString().includes(search)) ||
            (item.impressions?.toString().includes(search)) ||
            (item.clicks?.toString().includes(search))
        );
    }

    filteredData.sort((a, b) => {
        const aValue = a[orderBy as keyof RevenueData] || '';
        const bValue = b[orderBy as keyof RevenueData] || '';

        if (orderDir === 'asc') {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        } else {
            return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
        }
    });

    const total = groupedData.length;
    const filteredCount = filteredData.length;
    const paginatedData = length === -1
        ? filteredData
        : filteredData.slice(start, start + length);

    return {
        data: paginatedData,
        total: total,
        filtered: filteredCount
    };
};
const getMappedStyleIds = async (userId: string): Promise<string[]> => {
    try {
        const mappings = await prisma.styleIdUserMappings.findMany({
            where: { UserId: userId },
            select: { StyleId: true }
        });

        return mappings
            .map(mapping => mapping.StyleId)
            .filter((id): id is string => id !== null && id !== undefined);
    } catch (error) {
        console.error('Error fetching mapped style IDs:', error);
        return [];
    }
};

const validateSelections = async (selectedChannels: string[], selectedStyles: string[], userId: string, user: AuthenticatedUser): Promise<ValidationResult> => {
    const validChannelIds: string[] = [];
    const channelDimensions: string[] = [];

    // Channel validation
    if (selectedChannels && selectedChannels.length > 0) {
        for (const channelId of selectedChannels) {
            const channel = await prisma.channals.findUnique({ where: { Id: channelId } });
            if (channel && channel.ReportingDimensionId) {
                validChannelIds.push(channelId);
                channelDimensions.push(channel.ReportingDimensionId);
            }
        }
    }

    const validStyleIds: string[] = [];
    const styleDimensions: string[] = [];

    // Style validation
    if (selectedStyles && selectedStyles.length > 0) {
        if (user.User_Type === 'Super Admin' || user.User_Type === 'Admin') {
            // Admins can access all selected styles
            for (const styleId of selectedStyles) {
                const style = await prisma.styleIds.findUnique({ where: { Id: styleId } });
                if (style && style.StyleId) {
                    validStyleIds.push(styleId);
                    styleDimensions.push(style.StyleId);
                }
            }
        } else {
            // Non-admins can ONLY access their explicitly mapped styles
            const mappedStyleIds = await prisma.styleIdUserMappings.findMany({
                where: { UserId: userId },
                select: { StyleId: true }
            });

            const validMappedIds = mappedStyleIds
                .map(mapping => mapping.StyleId)
                .filter((id): id is string => id !== null && id !== undefined);

            for (const styleId of selectedStyles) {
                if (validMappedIds.includes(styleId)) {
                    const style = await prisma.styleIds.findUnique({ where: { Id: styleId } });
                    if (style && style.StyleId) {
                        validStyleIds.push(styleId);
                        styleDimensions.push(style.StyleId);
                    }
                }
            }
        }
    }

    return {
        validChannelIds,
        validStyleIds,
        channelDimensions,
        styleDimensions,
        hasValidChannels: validChannelIds.length > 0,
        hasValidStyles: validStyleIds.length > 0
    };
};

const getDataFromDB = async (
    startDate: string,
    endDate: string,
    selectedChannels: string[],
    selectedStyles: string[],
    userId: string,
    user: AuthenticatedUser,
    breakpoints: Breakpoints = {}
): Promise<any[]> => {
    try {
        const validation = await validateSelections(selectedChannels, selectedStyles, userId, user);

        // Only restrict access for non-admins when styles are specifically selected but none are valid
        if (user.User_Type !== 'Super Admin' && user.User_Type !== 'Admin' &&
            selectedStyles.length > 0 && !validation.hasValidStyles) {
            return [];
        }

        const startDateTime = new Date(startDate);
        startDateTime.setUTCHours(0, 0, 0, 0);

        const endDateTime = new Date(endDate);
        endDateTime.setUTCHours(23, 59, 59, 999);

        const whereClause: any = {
            Date: {
                gte: startDateTime,
                lte: endDateTime
            }
        };

        const andConditions = [];

        // Only add channel filter if channels are specifically selected
        if (selectedChannels.length > 0 && validation.channelDimensions.length > 0) {
            andConditions.push({
                ChannalId: { in: validation.channelDimensions }
            });
        }

        // Only add style filter if styles are specifically selected
        if (selectedStyles.length > 0 && validation.styleDimensions.length > 0) {
            andConditions.push({
                StyleId: { in: validation.styleDimensions }
            });
        }

        // Apply filters only if there are conditions
        if (andConditions.length > 0) {
            if (selectedChannels.length > 0 && selectedStyles.length > 0) {
                whereClause.AND = andConditions;
            } else {
                Object.assign(whereClause, andConditions[0]);
            }
        }

        const dbData = await prisma.revenue.findMany({
            where: whereClause,
            include: {
                Channals: {
                    select: {
                        DisplayName: true,
                        ReportingDimensionId: true
                    }
                },
                // StyleIds: {
                //     select: {
                //         StyleId: true,
                //         Name: true
                //     }
                // }
            },
            orderBy: { Date: 'asc' }
        });

        return dbData;
    } catch (error) {
        console.error('Database query error:', error);
        return [];
    }
};

const convertDBDataToJSON = async (dbData: any[]): Promise<RevenueData[]> => {
    const jsonData: RevenueData[] = [];

    const uniqueStyleIds = [...new Set(dbData.map(row => row.StyleId).filter(Boolean))];

    const styleIdsData = await prisma.styleIds.findMany({
        where: {
            StyleId: {
                in: uniqueStyleIds
            }
        },
        select: {
            StyleId: true,
            Name: true
        }
    });

    const styleIdsMap = new Map(
        styleIdsData.map(style => [style.StyleId, style.Name])
    );

    for (const row of dbData) {
        const customChannelId = row.ChannalId || '';
        const customSearchStyleId = row.StyleId || '';
        const customChannelName = row.channal?.DisplayName || '';

        const channelIdNum = customChannelId.includes(':')
            ? customChannelId.split(':')[1]
            : customChannelId;

        const styleIdNum = customSearchStyleId.length >= 9
            ? customSearchStyleId.slice(-9)
            : customSearchStyleId;
        const customSearchStyleName = styleIdsMap.get(customSearchStyleId) || '';

        const estimatedEarnings = row.EstimatedEarnings !== null ?
            parseFloat(row.EstimatedEarnings.toString()) : 0;

        const impressionsRpm = row.ImpressionsRpm !== null ?
            parseFloat(row.ImpressionsRpm.toString()) : 0;

        const impressionsCtr = row.ImpressionsCtr !== null ?
            parseFloat(row.ImpressionsCtr.toString()) : 0;

        const costPerClick = row.CostPerClick !== null ?
            parseFloat(row.CostPerClick.toString()) : 0;

        jsonData.push({
            country: row.Country || '',
            platformType: row.PlatfromType || '',
            date: row.Date.toISOString().split('T')[0],
            customChannelId: customChannelId,
            customChannelName: customChannelName,
            customChannel: `${customChannelName}[${channelIdNum}]`,
            customSearchStyleId: customSearchStyleId,
            customSearchStyleName: customSearchStyleName,
            customSearchStyle: `${customSearchStyleName}[${styleIdNum}]`,
            estimatedEarnings: estimatedEarnings,
            impressions: parseInt(row.Impressions?.toString() || '0') || 0,
            impressionsRpm: impressionsRpm,
            clicks: parseInt(row.Clicks?.toString() || '0') || 0,
            impressionsCtr: impressionsCtr,
            costPerClick: costPerClick
        });
    }

    return jsonData;
};
const convertCSVToJSON = async (csvString: string): Promise<RevenueData[]> => {
    if (!csvString || csvString.trim() === '') {
        return [];
    }

    const lines = csvString.split('\n').filter(line => line.trim() !== '');
    if (lines.length <= 1) {
        return [];
    }

    const headers = lines[0].split(',').map(h => h.trim());
    const jsonData: RevenueData[] = [];

    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = [];
        let current = '';
        let inQuotes = false;

        for (let j = 0; j < line.length; j++) {
            const char = line[j];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        values.push(current.trim());

        const cleanValues = values.map(val => val.replace(/^"|"$/g, '').trim());

        if (cleanValues.length < 8) {
            continue;
        }

        const row: RevenueData = {
            country: cleanValues[0] || '',
            platformType: cleanValues[1] || '',
            date: cleanValues[2] || '',
            customChannelName: cleanValues[3] || '',
            customChannelId: cleanValues[4] || '',
            customSearchStyleName: cleanValues[5] || '',
            customSearchStyleId: cleanValues[6] || '',
            estimatedEarnings: parseFloat(cleanValues[7]) || 0,
            impressions: parseInt(cleanValues[8]) || 0,
            impressionsRpm: parseFloat(cleanValues[9]) || 0,
            clicks: parseInt(cleanValues[10]) || 0,
            impressionsCtr: parseFloat(cleanValues[11]) || 0,
            costPerClick: parseFloat(cleanValues[12]) || 0
        };

        jsonData.push(row);
    }

    return jsonData;
};

const upsertReportData = async (combinedReport: string): Promise<void> => {
    if (!combinedReport || combinedReport.trim() === '') {
        return;
    }

    try {
        const lines = combinedReport.split('\n').filter(line => line.trim() !== '');

        if (lines.length <= 1) {
            return;
        }

        const headers = lines[0].split(',').map(h => h.trim());
        const rows = lines.slice(1);
        const chunkSize = 25;
        let totalProcessed = 0;
        let totalErrors = 0;

        for (let i = 0; i < rows.length; i += chunkSize) {
            const chunk = rows.slice(i, i + chunkSize);
            const batchUpserts = [];

            for (const row of chunk) {
                if (!row.trim()) continue;

                try {
                    const values = [];
                    let current = '';
                    let inQuotes = false;

                    for (let j = 0; j < row.length; j++) {
                        const char = row[j];
                        if (char === '"') {
                            inQuotes = !inQuotes;
                        } else if (char === ',' && !inQuotes) {
                            values.push(current.trim());
                            current = '';
                        } else {
                            current += char;
                        }
                    }
                    values.push(current.trim());

                    const cleanColumns = values.map(col => col.replace(/^"|"$/g, '').trim());

                    if (cleanColumns.length < 8) {
                        continue;
                    }

                    const [
                        countryName,
                        platformTypeName,
                        date,
                        customChannelName,
                        customChannelId,
                        customSearchStyleName,
                        customSearchStyleId,
                        estimatedEarnings,
                        impressions,
                        impressionsRpm,
                        clicks,
                        impressionsCtr,
                        costPerClick
                    ] = cleanColumns;

                    const formattedDate = date ? new Date(date) : null;
                    if (!formattedDate || isNaN(formattedDate.getTime())) {
                        continue;
                    }

                    const processValue = (value: string) => value?.toString().trim() || "";
                    const processNumericValue = (value: string) => {
                        if (!value || value.trim() === '') return 0;
                        const num = parseFloat(value.toString());
                        return isNaN(num) ? 0 : num;
                    };

                    const finalChannelId = processValue(customChannelId);
                    const finalStyleId = processValue(customSearchStyleId);
                    const finalCountry = processValue(countryName);
                    const finalPlatformType = processValue(platformTypeName);

                    const uniqueIdentifier = {
                        Date: formattedDate,
                        StyleId: finalStyleId,
                        ChannalId: finalChannelId,
                        Country: finalCountry,
                        PlatfromType: finalPlatformType
                    };

                    const revenueData = {
                        EstimatedEarnings: processNumericValue(estimatedEarnings),
                        Impressions: Math.floor(processNumericValue(impressions)),
                        ImpressionsRpm: processNumericValue(impressionsRpm),
                        Clicks: Math.floor(processNumericValue(clicks)),
                        ImpressionsCtr: processNumericValue(impressionsCtr),
                        CostPerClick: processNumericValue(costPerClick)
                    };

                    batchUpserts.push(
                        prisma.revenue.upsert({
                            where: {
                                Date_StyleId_ChannalId_Country_PlatfromType: uniqueIdentifier
                            },
                            create: {
                                ...uniqueIdentifier,
                                ...revenueData
                            },
                            update: revenueData
                        })
                    );

                } catch (parseError) {
                    totalErrors++;
                    continue;
                }
            }

            if (batchUpserts.length > 0) {
                try {
                    const results = await Promise.all(batchUpserts);
                    totalProcessed += batchUpserts.length;
                } catch (batchError) {
                    totalErrors += batchUpserts.length;

                    for (const upsertOperation of batchUpserts) {
                        try {
                            await upsertOperation;
                            totalProcessed++;
                        } catch (individualError) {
                            totalErrors++;
                        }
                    }
                }
            }

            if (i + chunkSize < rows.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

    } catch (error) {
        console.error('Upsert error:', error);
        throw error;
    }
};

export async function POST(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            User_Type: string;
            Id: string;
        };

        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user?.Id) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const code = searchParams.get("code");
        const startDateParams = searchParams.get("startDate");
        const endDateParams = searchParams.get("endDate");
        const breakPoints = searchParams.get("breakPoints") || "{}";
        const toggle = searchParams.get("toggle") || "false";
        const paramUserId = searchParams.get("userId");
        const start = parseInt(searchParams.get("start") || "0");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("search") || '';
        const orderBy = searchParams.get("orderBy") || "date";
        const orderDir = searchParams.get("orderDir") || "asc";
        const draw = parseInt(searchParams.get("draw") || "1");

        const isAdmin = user.User_Type === 'Super Admin' || user.User_Type === 'Admin';
        const userId = paramUserId && isAdmin ? paramUserId : user.Id;

        let parsedBreakpoints: Breakpoints;
        try {
            parsedBreakpoints = JSON.parse(breakPoints);
        } catch (error) {
            parsedBreakpoints = {};
        }

        const body = await req.json();
        let selectedChannels: string[] = body.selectedChannels || [];
        let selectedStyles: string[] = body.selectedStyles || [];

        if (parsedBreakpoints.customChannel && selectedChannels.length === 0) {
            const allChannels = await prisma.channals.findMany({
                select: { Id: true }
            });
            selectedChannels = allChannels.map(channel => channel.Id);
        }

        if (parsedBreakpoints.styleId && selectedStyles.length === 0) {

            const targetUserId = paramUserId;

            if (user.User_Type === 'Super Admin' || user.User_Type === 'Admin') {
                if (paramUserId) {
                    const mappedStyleIds = await prisma.styleIdUserMappings.findMany({
                        where: { UserId: targetUserId },
                        select: { StyleId: true }
                    });

                    selectedStyles = mappedStyleIds
                        .map(mapping => mapping.StyleId)
                        .filter((id): id is string => id !== null && id !== undefined);

                    if (selectedStyles.length === 0) {
                        return NextResponse.json({
                            message: `No style IDs assigned to user ${targetUserId}`,
                            data: []
                        }, { status: 403 });
                    }
                } else {
                    const allStyles = await prisma.styleIds.findMany({
                        select: { Id: true }
                    });
                    selectedStyles = allStyles.map(style => style.Id);
                }
            } else {
                const mappedStyleIds = await prisma.styleIdUserMappings.findMany({
                    where: { UserId: userId },
                    select: { StyleId: true }
                });

                selectedStyles = mappedStyleIds
                    .map(mapping => mapping.StyleId)
                    .filter((id): id is string => id !== null && id !== undefined);

                if (selectedStyles.length === 0) {
                    return NextResponse.json({
                        message: "No style IDs assigned to your account",
                        data: []
                    }, { status: 403 });
                }
            }
        }

        const userSettings = await prisma.adminUserSetting.findFirst({});
        if (!userSettings?.RevenueClientId || !userSettings?.RevenueClientSecret) {
            return NextResponse.json(
                { message: 'OAuth credentials not found in database' },
                { status: 404 }
            );
        }

        const oauth2Client = new google.auth.OAuth2(
            userSettings.RevenueClientId,
            userSettings.RevenueClientSecret,
            process.env.REDIRECT_URI
        );

        const authUrl = oauth2Client.generateAuthUrl({
            access_type: 'offline',
            scope: [
                'https://www.googleapis.com/auth/adsense.readonly',
                'https://www.googleapis.com/auth/adsense',
            ],
            prompt: 'consent',
            include_granted_scopes: true,
        });

        let tokens = null;
        if (userSettings.RevenueAccessToken && userSettings.RevenueRefreshToken) {
            tokens = {
                access_token: userSettings.RevenueAccessToken,
                refresh_token: userSettings.RevenueRefreshToken,
            };
        }

        if (!code && !tokens) {
            return NextResponse.redirect(authUrl);
        }

        if (code) {
            try {
                const { tokens: newTokens } = await oauth2Client.getToken(code);
                oauth2Client.setCredentials(newTokens);
                tokens = newTokens;

                await prisma.adminUserSetting.updateMany({
                    data: {
                        RevenueAccessToken: newTokens.access_token,
                        RevenueRefreshToken: newTokens.refresh_token,
                    },
                });

                return NextResponse.json(
                    { message: 'Authorization successful', tokens: newTokens },
                    { status: 200 }
                );
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error during token exchange';
                return NextResponse.json(
                    { error: 'Failed to exchange authorization code for tokens', details: errorMessage },
                    { status: 500 }
                );
            }
        }

        if (tokens) {
            oauth2Client.setCredentials(tokens);

            try {
                const adsense = google.adsense({ version: 'v2', auth: oauth2Client });
                await adsense.accounts.list({ pageSize: 1 });
            } catch (error) {
                const isAuthError =
                    error instanceof Error &&
                    (error.message.includes('invalid_token')) ||
                    (typeof (error as any).code === 'number' && (error as any).code === 401);

                if (isAuthError) {
                    try {
                        const { credentials } = await oauth2Client.refreshAccessToken();
                        tokens = {
                            access_token: credentials.access_token,
                            refresh_token: credentials.refresh_token || userSettings.RevenueRefreshToken,
                        };

                        await prisma.adminUserSetting.updateMany({
                            data: {
                                RevenueAccessToken: credentials.access_token,
                                RevenueRefreshToken: credentials.refresh_token || userSettings.RevenueRefreshToken,
                            },
                        });

                        oauth2Client.setCredentials(tokens);
                    } catch (refreshError) {
                        return NextResponse.redirect(authUrl);
                    }
                } else {
                    throw error;
                }
            }
        }

        const fetchChannelsRecord = async (Id: string) => {
            const record = await prisma.channals.findUnique({ where: { Id: Id } });
            return userSettings?.PubId
                ? `partner-pub-${userSettings.PubId}:${record?.ReportingDimensionId?.split(":")[1]}`
                : record?.ReportingDimensionId;
        };

        const fetchStyleDimensionId = async (Id: string) => {
            const record = await prisma.styleIds.findUnique({ where: { Id: Id } });
            return record?.StyleId;
        };

        const today = new Date();
        const startDate = startDateParams ? new Date(startDateParams) : new Date();
        const endDate = endDateParams ? new Date(endDateParams) : new Date();

        const isToday = startDate.toDateString() === today.toDateString() && endDate.toDateString() === today.toDateString();

        if (isToday) {
            const finalStartDate = {
                day: today.getDate(),
                month: today.getMonth() + 1,
                year: today.getFullYear(),
            };
            const finalEndDate = finalStartDate;

            const metrics = [
                "ESTIMATED_EARNINGS",
                "IMPRESSIONS",
                "IMPRESSIONS_RPM",
                "CLICKS",
                "IMPRESSIONS_CTR",
                "COST_PER_CLICK",
            ];

            let allReportsData: string[] = [];

            if (selectedChannels.length > 0) {
                const channelDimensions = await Promise.all(
                    selectedChannels.map(fetchChannelsRecord)
                );
                const validChannels = channelDimensions.filter(Boolean);

                if (validChannels.length > 0) {
                    const channelReport = await generateReport(
                        oauth2Client,
                        userSettings.AdsAccountId,
                        google,
                        finalStartDate,
                        finalEndDate,
                        metrics,
                        parsedBreakpoints,
                        validChannels,
                        toggle
                    );

                    if (typeof channelReport === 'string' && channelReport.trim()) {
                        const lines = channelReport.split('\n');
                        if (lines.length > 1) {
                            const [currentHeader, ...rows] = lines;
                            const filteredRows = rows.filter(r => r.trim() !== '');
                            allReportsData.push(...filteredRows);
                        }
                    }
                }
            }

            if (selectedStyles.length > 0) {
                const styleDimensions = await Promise.all(
                    selectedStyles.map(fetchStyleDimensionId)
                );
                const validStyles = styleDimensions.filter(Boolean);

                if (validStyles.length > 0) {
                    const styleReport = await generateReport(
                        oauth2Client,
                        userSettings.AdsAccountId,
                        google,
                        finalStartDate,
                        finalEndDate,
                        metrics,
                        parsedBreakpoints,
                        validStyles,
                        toggle
                    );

                    if (typeof styleReport === 'string' && styleReport.trim()) {
                        const lines = styleReport.split('\n');
                        if (lines.length > 1) {
                            const [currentHeader, ...rows] = lines;
                            const filteredRows = rows.filter(r => r.trim() !== '');
                            allReportsData.push(...filteredRows);
                        }
                    }
                }
            }

            if (allReportsData.length > 0) {
                const csvHeader = 'Country,Platform Type,Date,Custom Channel Name,Custom Channel Id,Custom Search Style Name,Custom Search Style Id,Estimated Earnings,Impressions,Impressions RPM,Clicks,Impressions CTR,Cost Per Click';
                const csvString = [csvHeader, ...allReportsData].join('\n');

                const jsonData = await convertCSVToJSON(csvString);

                if (jsonData.length > 0) {
                    await upsertReportData(csvString);

                    const dbData = await getDataFromDB(
                        startDateParams || startDate.toISOString().split('T')[0],
                        endDateParams || endDate.toISOString().split('T')[0],
                        selectedChannels,
                        selectedStyles,
                        userId,
                        user
                    );

                    if (dbData.length > 0) {
                        const dbJsonData = await convertDBDataToJSON(dbData);
                        const { data: filteredData, total, filtered } = filterDataByBreakpoints(
                            dbJsonData,
                            parsedBreakpoints,
                            selectedChannels,
                            selectedStyles,
                            search,
                            orderBy,
                            orderDir,
                            start,
                            length
                        );

                        return NextResponse.json({
                            success: true,
                            data: filteredData,
                            pagination: {
                                draw,
                                recordsTotal: total,
                                recordsFiltered: filtered,
                                currentPageCount: filteredData.length,
                                start,
                                length,
                                currentPage: Math.floor(start / (length || 1)) + 1,
                                totalPages: length === -1 ? 1 : Math.ceil(filtered / (length || 1)),
                                hasNextPage: length === -1 ? false : start + length < filtered,
                                hasPreviousPage: start > 0,
                            }
                        }, { status: 200 });
                    } else {
                        const { data: filteredData, total, filtered } = filterDataByBreakpoints(
                            jsonData,
                            parsedBreakpoints,
                            selectedChannels,
                            selectedStyles,
                            search,
                            orderBy,
                            orderDir,
                            start,
                            length
                        );

                        return NextResponse.json({
                            success: true,
                            data: filteredData,
                            pagination: {
                                draw,
                                recordsTotal: total,
                                recordsFiltered: filtered,
                                currentPageCount: filteredData.length,
                                start,
                                length,
                                currentPage: Math.floor(start / (length || 1)) + 1,
                                totalPages: length === -1 ? 1 : Math.ceil(filtered / (length || 1)),
                                hasNextPage: length === -1 ? false : start + length < filtered,
                                hasPreviousPage: start > 0,
                            }
                        }, { status: 200 });
                    }
                } else {
                    return NextResponse.json({
                        message: "Failed to process API data",
                        data: []
                    }, { status: 404 });
                }
            } else {
                return NextResponse.json({
                    message: "No revenue data found for today",
                    data: []
                }, { status: 404 });
            }
        } else {
            const dbData = await getDataFromDB(
                startDateParams || startDate.toISOString().split('T')[0],
                endDateParams || endDate.toISOString().split('T')[0],
                selectedChannels,
                selectedStyles,
                userId,
                user
            );

            if (dbData.length > 0) {
                const jsonData = await convertDBDataToJSON(dbData);
                const { data: filteredData, total, filtered } = filterDataByBreakpoints(
                    jsonData,
                    parsedBreakpoints,
                    selectedChannels,
                    selectedStyles,
                    search,
                    orderBy,
                    orderDir,
                    start,
                    length
                );

                return NextResponse.json({
                    success: true,
                    data: filteredData,
                    pagination: {
                        draw,
                        recordsTotal: total,
                        recordsFiltered: filtered,
                        currentPageCount: filteredData.length,
                        start,
                        length,
                        currentPage: Math.floor(start / (length || 1)) + 1,
                        totalPages: length === -1 ? 1 : Math.ceil(filtered / (length || 1)),
                        hasNextPage: length === -1 ? false : start + length < filtered,
                        hasPreviousPage: start > 0,
                    }
                }, { status: 200 });
            } else {
                return NextResponse.json({
                    message: "No revenue data found ",
                }, { status: 404 });
            }
        }

    } catch (error) {
        console.error('API Error:', error);
        return NextResponse.json({
            error: "Internal server error",
            details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}