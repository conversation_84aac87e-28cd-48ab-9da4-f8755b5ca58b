import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const orderBy = searchParams.get("orderBy") || "ClickCount";
        const orderDir = searchParams.get("orderDir") || "asc";
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");
        const url = searchParams.get("url");
        const domain = searchParams.get("domain");

        // Validate required date range
        if (!startDate || !endDate) {
            return NextResponse.json({ error: "startDate and endDate are required." }, { status: 400 });
        }

        // Validate URL and domain
        if (!url || !domain) {
            return NextResponse.json({ error: "url and domain are required." }, { status: 400 });
        }

        // Construct where condition
        const whereCondition: any = {
            Created_At: {
                gte: new Date(startDate + 'T00:00:00.000Z'),
                lte: new Date(endDate + 'T23:59:59.999Z')
            },
            URL: url,
            Domain: domain
        };

        // Optional search filter
        if (search && search !== "") {
            whereCondition.OR = [
                { URL: { contains: search, mode: 'insensitive' } },
                { Keyword: { contains: search, mode: 'insensitive' } },
                { IpAddress: { contains: search, mode: 'insensitive' } },
                { Domain: { contains: search, mode: 'insensitive' } }
            ];
        }

        // Aggregate data
        const aggregatedData = await prisma.queryAnalytics.groupBy({
            by: ['URL', 'Keyword', 'IpAddress', 'Domain', 'Created_At'],
            _sum: {
                Count: true,
                AdsClickCounter: true
            },
            where: whereCondition,
            orderBy:
                orderBy === 'ClickCount'
                    ? { _sum: { Count: orderDir as 'asc' | 'desc' } }
                    : orderBy === 'AdsClickCount'
                        ? { _sum: { AdsClickCounter: orderDir as 'asc' | 'desc' } }
                        : orderBy === 'URL'
                            ? { URL: orderDir as 'asc' | 'desc' }
                            : orderBy === 'Keyword'
                                ? { Keyword: orderDir as 'asc' | 'desc' }
                                : orderBy === 'IpAddress'
                                    ? { IpAddress: orderDir as 'asc' | 'desc' }
                                    : { Domain: orderDir as 'asc' | 'desc' }
        });


        const transformedData = aggregatedData.map(item => ({
            ClickCount: item._sum.Count || 0,
            AdsClickCount: item._sum.AdsClickCounter || 0,
            URL: item.URL,
            Keyword: item.Keyword,
            IpAddress: item.IpAddress,
            Domain: item.Domain,
            Date: item.Created_At
        }));

        const totalRecords = transformedData.length;

        // Paginate
        let paginatedData = transformedData;
        if (length !== -1) {
            const skip = (start - 1) * length;
            paginatedData = transformedData.slice(skip, skip + length);
        }


        return NextResponse.json({
            success: true,
            data: paginatedData,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
