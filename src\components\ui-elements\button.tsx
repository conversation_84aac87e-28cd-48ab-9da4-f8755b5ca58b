import { cva, VariantProps } from "class-variance-authority";
import type { HTMLAttributes } from "react";


const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2.5 text-center font-medium hover:bg-opacity-90 transition focus:outline-none",
  {
    variants: {
      variant: {
        primary: "bg-primary text-white",
        green: "bg-green text-white",
        danger: "bg-red-600 text-white",
        dark: "bg-dark text-white dark:bg-white/10",
        outlinePrimary: "border border-primary hover:bg-primary/10 text-primary",
        outlineGreen: "border border-green hover:bg-green/10 text-green",
        outlineDark:
          "border border-dark hover:bg-dark/10 text-dark dark:hover:bg-white/10 dark:border-white/25 dark:text-white",
      },
      shape: {
        default: "",
        rounded: "rounded-[5px]",
        full: "rounded-full",
      },
      size: {
        default: "py-3.5 px-10 lg:px-8 xl:px-10",
        small: "py-[11px] px-6",
      },
      disabled: {
        true: "bg-gray-600 text-gray-300 opacity-50 cursor-not-allowed",
      },
    },
    defaultVariants: {
      variant: "primary",
      shape: "default",
      size: "default",
      disabled: false,
    },
    compoundVariants: [
      {
        variant: ["outlinePrimary", "outlineGreen", "outlineDark"],
        disabled: true,
        class: "bg-gray-600/10 text-gray-400 opacity-50 cursor-not-allowed",
      },
    ],
  },
);

type ButtonProps = HTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants> & {
    label: string;
    icon?: React.ReactNode;
    type?: "button" | "submit" | "reset";
    disabled?: boolean;
  };

export function Button({
  label,
  icon,
  variant,
  shape,
  size,
  className,
  type = "button",
  disabled = false,
  ...props
}: ButtonProps) {
  return (
    <button
      type={type}
      disabled={disabled}
      className={buttonVariants({ variant, shape, size, disabled, className })}
      {...props}
    >
      {icon && <span>{icon}</span>}
      {label}
    </button>
  );
}