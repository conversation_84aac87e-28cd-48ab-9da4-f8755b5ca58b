import { cn } from "@/lib/utils";
import {
  type HTMLInputTypeAttribute,
  useId,
  useState,
  useRef,
  useEffect,
} from "react";

type InputGroupProps = {
  className?: string;
  label: string;
  placeholder: string;
  type: HTMLInputTypeAttribute;
  fileStyleVariant?: "style1" | "style2";
  required?: boolean;
  disabled?: boolean;
  active?: boolean;
  handleChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  value?: string;
  name?: string;
  error?: string;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
  height?: "sm" | "default";
  defaultValue?: string;
};

const InputGroup: React.FC<InputGroupProps> = ({
  className,
  label,
  type,
  placeholder,
  required,
  disabled,
  active,
  error,
  handleChange,
  icon,
  ...props
}) => {
  const id = useId();
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const isFileInput = type === "file";

  useEffect(() => {
    if (props.value || props.defaultValue) {
      setHasValue(true);
    }
  }, [props.value, props.defaultValue]);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHasValue(!!e.target.value);
    if (handleChange) {
      handleChange(e);
    }
  };

  return (
    <div className={cn("relative", className)}>
      {/* Regular label for file inputs */}
      {isFileInput && (
        <label
          htmlFor={id}
          className="mb-1 block text-body-sm font-bold text-dark dark:text-white"
        >
          {label}
          {required && <span className="ml-1 select-none text-red">*</span>}
        </label>
      )}

      <div
        className={cn(
          "relative",
          isFileInput ? "" : "pt-5",
          "[&_svg]:absolute [&_svg]:top-1/2",
          props.iconPosition === "left"
            ? "[&_svg]:left-4.5"
            : "[&_svg]:right-4.5",
        )}
      >
        <input
          ref={inputRef}
          id={id}
          type={type}
          name={props.name}
          placeholder={isFocused && !isFileInput ? placeholder : ""}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          value={props.value}
          defaultValue={props.defaultValue}
          className={cn(
            "w-full rounded-lg border-[1.5px] border-stroke bg-transparent outline-none transition-all focus:border-primary disabled:cursor-default disabled:bg-gray-2 data-[active=true]:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary dark:disabled:bg-dark dark:data-[active=true]:border-primary",
            isFileInput
              ? getFileStyles(props.fileStyleVariant!)
              : "px-4 py-3 text-dark placeholder:text-dark-6 dark:text-white",
            props.iconPosition === "left" && "pl-12.5",
            props.height === "sm" && "py-2.5",
          )}
          required={required}
          disabled={disabled}
          data-active={active}
        />

        {/* Floating label for non-file inputs */}
        {!isFileInput && (
          <label
            htmlFor={id}
            className={cn(
              "absolute left-4 top-[65%]  -translate-y-[55%] cursor-text text-md text-dark-6 transition-all duration-200 dark:text-white",
              (isFocused || hasValue) &&
                "left-3 top-3 -translate-y-0 bg-white px-1 text-xs text-primary dark:bg-dark-2",
              isFocused && "text-primary",
              error && "text-red-400",
              props.iconPosition === "left" && "left-12.5",
            )}
            onClick={() => inputRef.current?.focus()}
          >
            {label}
            {required && <span className="ml-1 select-none text-red">*</span>}
          </label>
        )}

        {icon}
      </div>

      {error && (
        <p className="absolute -bottom-5 ml-1 mt-1 text-xs text-red-400">
          {error}
        </p>
      )}
    </div>
  );
};

export default InputGroup;

function getFileStyles(variant: "style1" | "style2") {
  switch (variant) {
    case "style1":
      return `file:mr-5 file:border-collapse file:cursor-pointer file:border-0 file:border-r file:border-solid file:border-stroke file:bg-[#E2E8F0] file:px-6.5 file:py-[13px] file:text-body-sm file:font-medium file:text-dark-5 file:hover:bg-primary file:hover:bg-opacity-10 dark:file:border-dark-3 dark:file:bg-white/30 dark:file:text-white`;
    default:
      return `file:mr-4 file:rounded file:border-[0.5px] file:border-stroke file:bg-stroke file:px-2.5 file:py-1 file:text-body-xs file:font-medium file:text-dark-5 file:focus:border-primary dark:file:border-dark-3 dark:file:bg-white/30 dark:file:text-white px-3 py-[9px]`;
  }
}