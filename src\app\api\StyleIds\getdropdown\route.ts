import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            User_Type: string;
            Id: string;
        };

        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const userIdFromQuery = searchParams.get('userId');

        let where: any = {
            IsDeleted: false
        };

        // If specific userId query provided
        if (userIdFromQuery) {
            where.CreatedBy = userIdFromQuery;
        }
        // For regular users (non-admin)
        else if (user.User_Type !== 'Super Admin' && user.User_Type !== 'Admin') {
            where.StyleIdUserMappings = {
                some: {
                    UserId: user.Id
                }
            };
        }
        // For Admins/Super Admins - no additional user filtering needed

        const stylesWithUsers = await prisma.styleIds.findMany({
            where,
            orderBy: {
                CreatedAt: 'desc'
            },
            select: {
                Id: true,
                Name: true,
            }
        });

        const response = NextResponse.json({
            success: true,
            data: stylesWithUsers,
        });
        return response;

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}