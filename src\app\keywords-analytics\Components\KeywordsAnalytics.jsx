"use client";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { formatDate } from "@/utils/functions";
import { FaChevronDown, FaChevronRight } from "react-icons/fa";
import Swal from "sweetalert2";
import { Button } from "@/components/ui-elements/button";
import DateRangePicker from "../../../components/DateRangePicker";

const KeywordsAnalytics = () => {
  const [keywordsData, setKeywordsData] = useState([]);
  const [grandTotals, setGrandTotals] = useState({});
  const [showLoader, setShowLoader] = useState(false);
  const [expandedRows, setExpandedRows] = useState({});
  const [keywordDetails, setKeywordDetails] = useState({});
  const [loadingKeywords, setLoadingKeywords] = useState({});
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());

  const formatDateForAPI = (date) => {
    if (!date) return "";
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const calculateCTR = (adsCount, totalCount) => {
    if (!totalCount || totalCount === 0) return 0;
    return (adsCount / totalCount) * 100;
  };

  const extractBlogName = (url) => {
    if (!url) return "N/A";
    try {
      const domain = new URL(url.startsWith("http") ? url : `https://${url}`)
        .hostname;
      return domain.replace("www.", "").split(".")[0];
    } catch {
      return url.length > 30 ? `${url.substring(0, 30)}...` : url;
    }
  };
  const fetchKeywordsData = async () => {
    setShowLoader(true);
    setExpandedRows({});
    setKeywordDetails({});

    try {
      const formattedStartDate = formatDateForAPI(startDate);
      const formattedEndDate = formatDateForAPI(endDate);

      const response = await axios.get("/api/KeywordAnalytics/GetDetails", {
        params: {
          startDate: formattedStartDate,
          endDate: formattedEndDate,
        },
        withCredentials: true,
      });

      if (response.data.success) {
        const formattedData = response.data.data.map((item) => ({
          ...item,
          CTR:
            calculateCTR(item.AdsClickCount, item.ClickCount).toFixed(2) + "%",
          blogName: item.URL,
        }));
        setKeywordsData(formattedData);
        const totals = response.data.data.reduce(
          (acc, item) => {
            acc.totalClicks = (acc.totalClicks || 0) + (item.ClickCount || 0);
            acc.totalAdsClicks =
              (acc.totalAdsClicks || 0) + (item.AdsClickCount || 0);
            return acc;
          },
          { totalClicks: 0, totalAdsClicks: 0 },
        );
        setGrandTotals(totals);
      } else {
        throw new Error(response.data.error || "Failed to fetch data");
      }
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to fetch keywords analytics",
        timer: 3000,
        showConfirmButton: false,
      });
      setKeywordsData([]);
      setGrandTotals({});
    } finally {
      setShowLoader(false);
    }
  };

  const fetchKeywordDetails = async (url, domain) => {
    const key = `${url}-${domain}`;
    setLoadingKeywords((prev) => ({ ...prev, [key]: true }));

    try {
      const formattedStartDate = formatDateForAPI(startDate);
      const formattedEndDate = formatDateForAPI(endDate);

      const response = await axios.get("/api/KeywordAnalytics/GetSummarize", {
        params: {
          url,
          domain,
          startDate: formattedStartDate,
          endDate: formattedEndDate,
        },
        withCredentials: true,
      });

      if (response.data.success) {
        const formattedData = response.data.data.map((item) => ({
          ...item,
          CTR:
            calculateCTR(item.AdsClickCount, item.ClickCount).toFixed(2) + "%",
        }));
        setKeywordDetails((prev) => ({
          ...prev,
          [key]: formattedData,
        }));
      } else {
        throw new Error(
          response.data.error || "Failed to fetch keyword details",
        );
      }
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to fetch keyword details",
        timer: 3000,
        showConfirmButton: false,
      });
      setKeywordDetails((prev) => ({ ...prev, [key]: [] }));
    } finally {
      setLoadingKeywords((prev) => ({ ...prev, [key]: false }));
    }
  };

  const toggleRowExpansion = (url, domain) => {
    const key = `${url}-${domain}`;
    if (expandedRows[key]) {
      setExpandedRows((prev) => ({ ...prev, [key]: false }));
    } else {
      setExpandedRows((prev) => ({ ...prev, [key]: true }));
      if (!keywordDetails[key]) {
        fetchKeywordDetails(url, domain);
      }
    }
  };

  useEffect(() => {
    if (startDate && endDate) {
      fetchKeywordsData();
    }
  }, []);

  return (
    <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
      <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-white">Keywords Analytics</h1>
        </div>
      </div>

      <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-xl border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-start">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
            <div className="sm:min-w-70">
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onDateChange={(dates) => {
                  setStartDate(dates[0]);
                  setEndDate(dates[1]);
                }}
              />
            </div>
            <Button
              type="button"
              label="Apply"
              variant="primary"
              shape="rounded"
              onClick={fetchKeywordsData}
            />
          </div>
        </div>

        {Object.keys(grandTotals).length > 0 && (
          <div className="mt-6 grid grid-cols-1 gap-4 rounded-lg bg-gray-100 p-4 dark:bg-dark-2 md:grid-cols-4">
            <div className="flex flex-col items-center justify-center border-r border-gray-300 dark:border-dark-3 md:items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Grand Totals
              </span>
            </div>

            <div className="flex flex-col items-center justify-center border-r border-gray-300 dark:border-dark-3 md:items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Date Range
              </span>
              <span className="text-base font-semibold text-dark dark:text-white">
                {formatDate(startDate)} - {formatDate(endDate)}
              </span>
            </div>

            <div className="flex flex-col items-center justify-center border-r border-gray-300 dark:border-dark-3 md:items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Clicks
              </span>
              <span className="text-base font-semibold text-dark dark:text-white">
                {grandTotals.totalClicks?.toLocaleString() || 0}
              </span>
            </div>

            <div className="flex flex-col items-center justify-center md:items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Overall CTR
              </span>
              <span className="text-base font-semibold text-dark dark:text-white">
                {calculateCTR(
                  grandTotals.totalAdsClicks,
                  grandTotals.totalClicks,
                ).toFixed(2)}
                %
              </span>
            </div>
          </div>
        )}

        <div className="mt-6 overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
          {showLoader ? (
            <div className="flex h-64 items-center justify-center">
              <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-primary">
                <tr>
                  {[
                    "",
                    "Blog Name",
                    "Domain",
                    "Date",
                    "Count",
                    "Ads Count",
                    "(Ads Count/Count)",
                  ].map((header, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="w-12 px-6 py-5 text-left text-xs font-medium uppercase tracking-wider text-white"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {keywordsData.length > 0 ? (
                  keywordsData.map((row, index) => {
                    const key = `${row.URL}-${row.Domain}`;
                    return (
                      <React.Fragment key={index}>
                        <tr
                          className={`transition-colors hover:bg-blue-50 ${index % 2 === 0 ? "bg-gray-50" : "bg-white"}`}
                        >
                          <td className="whitespace-nowrap px-6 py-4 text-center">
                            <button
                              onClick={() =>
                                toggleRowExpansion(row.URL, row.Domain)
                              }
                              className="text-gray-600 hover:text-blue-600 focus:outline-none"
                            >
                              {expandedRows[key] ? (
                                <FaChevronDown className="h-4 w-4" />
                              ) : (
                                <FaChevronRight className="h-4 w-4" />
                              )}
                            </button>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="flex items-center">
                              <div className="text-sm font-medium text-gray-900">
                                {row.blogName}
                              </div>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="text-sm text-gray-900">
                              {row.Domain || "-"}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="text-sm text-gray-900">
                              {`${row.StartDate} - ${row.EndDate}`}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="text-sm text-gray-900">
                              {row.ClickCount?.toLocaleString() || 0}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="text-sm text-gray-900">
                              {row.AdsClickCount?.toLocaleString() || 0}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <span className="inline-flex rounded-full px-2 text-xs font-semibold leading-5">
                              {calculateCTR(
                                row.AdsClickCount,
                                row.ClickCount,
                              ).toFixed(2)}{" "}
                              %
                            </span>
                          </td>
                        </tr>

                        {expandedRows[key] && (
                          <tr className="bg-gray-50">
                            <td colSpan="7" className="px-0 py-0">
                              <div className="px-6 py-4">
                                {loadingKeywords[key] ? (
                                  <div className="flex items-center justify-center py-4">
                                    <div className="mr-3 h-5 w-5 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                                    <span>Loading details...</span>
                                  </div>
                                ) : (
                                  <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm">
                                    <table className="min-w-full divide-y divide-gray-200">
                                      <thead className="bg-gray-100">
                                        <tr>
                                          {[
                                            "Date",
                                            "Keyword",
                                            "Count",
                                            "Ads Clicks",
                                            "Count",
                                          ].map((header, idx) => (
                                            <th
                                              key={idx}
                                              scope="col"
                                              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700"
                                            >
                                              {header}
                                            </th>
                                          ))}
                                        </tr>
                                      </thead>
                                      <tbody className="divide-y divide-gray-200 bg-white">
                                        {keywordDetails[key]?.length > 0 ? (
                                          keywordDetails[key].map(
                                            (detail, idx) => (
                                              <tr
                                                key={idx}
                                                className="hover:bg-blue-50"
                                              >
                                                <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                                                  {detail.Date
                                                    ? formatDate(
                                                        new Date(detail.Date),
                                                      )
                                                    : "N/A"}
                                                </td>
                                                <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                                                  {detail.Keyword || "N/A"}
                                                </td>
                                                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                                                  {detail.ClickCount?.toLocaleString() ||
                                                    0}
                                                </td>
                                                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                                                  {detail.AdsClickCount?.toLocaleString() ||
                                                    0}
                                                </td>
                                                <td className="whitespace-nowrap px-6 py-4">
                                                  <span className="inline-flex rounded-full px-2 text-xs font-semibold leading-5">
                                                    {calculateCTR(
                                                      detail.AdsClickCount,
                                                      detail.ClickCount,
                                                    ).toFixed(2)}{" "}
                                                    %
                                                  </span>
                                                </td>
                                              </tr>
                                            ),
                                          )
                                        ) : (
                                          <tr>
                                            <td
                                              colSpan="5"
                                              className="px-6 py-4 text-center text-sm text-gray-500"
                                            >
                                              No detailed data available
                                            </td>
                                          </tr>
                                        )}
                                      </tbody>
                                    </table>
                                  </div>
                                )}
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    );
                  })
                ) : (
                  <tr>
                    <td
                      colSpan="7"
                      className="px-6 py-4 text-center text-sm text-gray-500"
                    >
                      {showLoader
                        ? "Loading..."
                        : "No keywords data available for the selected date range"}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default KeywordsAnalytics;
