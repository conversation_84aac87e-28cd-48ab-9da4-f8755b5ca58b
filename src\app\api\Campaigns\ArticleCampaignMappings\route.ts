import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function POST(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get query parameters from URL
        const { searchParams } = new URL(req.url);
        const articleId = searchParams.get('articleId');
        const campaignId = searchParams.get('campaignId');

        if (!articleId || !campaignId) {
            return NextResponse.json({ error: 'articleId and campaignId are required' }, { status: 400 });
        }

        // Check if mapping already exists
        const existingMapping = await prisma.articleCampaignMappings.findFirst({
            where: {
                ArticleId: articleId,
                CampaignId: parseInt(campaignId),
            },
        });

        if (existingMapping) {
            return NextResponse.json({
                success: true,
                message: "Mapping already exists",
            });
        }

        // Create new mapping
        const result = await prisma.articleCampaignMappings.create({
            data: {
                ArticleId: articleId,
                CampaignId: parseInt(campaignId),
            },
        });

        return NextResponse.json({
            success: true,
            data: result,
        });

    } catch (error) {
        console.error('Error in ArticleCampaignMapping POST:', error);
        return NextResponse.json(
            { error: 'Failed to add article-campaign mapping' },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
