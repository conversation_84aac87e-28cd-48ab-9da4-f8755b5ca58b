import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const domainId = searchParams.get("DomainId");
        if (!domainId) {
            return NextResponse.json(
                { error: "DomainId parameter is required" },
                { status: 400 }
            );
        }

        let where: any = {
            Domain: domainId,
            IsDeleted: false
        };

        // For Super Admin and Admin, show all subdomains in the domain
        if (user.User_Type === 'Super Admin' || user.User_Type === 'Admin') {
            const subdomainlist = await prisma.subDomain.findMany({
                where,
                orderBy: {
                    CreatedAt: 'desc'
                },
                select: {
                    Id: true,
                    Name: true,
                    Url: true
                }
            });

            return NextResponse.json({
                success: true,
                data: subdomainlist,
            });
        }

        // For regular users, filter by SubDomainUserMappings
        const subdomainlist = await prisma.subDomain.findMany({
            where: {
                ...where,
                SubDomainUserMappings: {
                    some: {
                        UserId: user.Id,
                    }
                }
            },
            orderBy: {
                CreatedAt: 'desc'
            },
            select: {
                Id: true,
                Name: true,
                Url: true
            }
        });

        return NextResponse.json({
            success: true,
            data: subdomainlist,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}