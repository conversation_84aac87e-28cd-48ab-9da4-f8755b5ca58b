"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import axios from "axios";
import { <PERSON>a<PERSON><PERSON>, FaFilter } from "react-icons/fa";
import { convertToBase64, fetchImage } from "@/utils/functions";
import Swal from "sweetalert2";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import ArticleModal from "../Components/ArticleModal"
import InputGroup from "@/components/FormElements/InputGroup";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import { decodeJWT } from "@/utils/functions";
import { CloseIcon } from "@/assets/icons";
import Checkbox from "@/components/FormElements/checkbox";

const reverseFieldMapping = {
  title: "Title",
  url: "url",
  description: "description",
  shortDescription: "shortDescription",
  metatitle: "metatitle",
  metadescription: "metadescription",
  metakeys: "metakeys",
  hashtag: "hashtag",
  customChannal: "customChannal",
  styleIdLm: "StyleIdLm",
  styleIdDm: "StyleIdDm",
  adrelatedsearches: "adrelatedsearches",
  remark: "remark",
  category: "category",
  userName: "user_id_settings",
  domain: "domain",
  subdomain: "subdomain",
  published: "Published",
  showArticle: "ShowArticle",
  showAds: "ShowsAds",
  image: "image",
  categoryName: "CategoryName",
  customChannalName: "CustomChannal",
  channelId: "CustomChannalId",
  campaignCount: "CampaignCount",
  status: "Published",
  userName: "UserName",
  domainName: "DomainName",
  subDomainName: "SubDomainName",
};

const ArticlePage = () => {
  const [categories, setCategories] = useState([]);
  const [modalShow, setModalShow] = useState(false);
  const [articles, setArticles] = useState([]);
  const [editId, setEditId] = useState(null);
  const [showLoader, setShowLoader] = useState(false);
  const [published, setPublished] = useState(true);
  const [showArticle, setShowArticle] = useState(false);
  const [showAds, setShowAds] = useState(false);
  const [formdataImage, setFormdataImage] = useState("");
  const [assignChannels, setAssignChannels] = useState([]);
  const [base64Image, setBase64Image] = useState("");
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [domains, setDomains] = useState([]);
  const [selectedDomain, setSelectedDomain] = useState("");
  const [subdomains, setSubdomains] = useState([]);
  const [selectedSubdomain, setSelectedSubdomain] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [editSlugMode, setEditSlugMode] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const [selectedCampaignIds, setSelectedCampaignIds] = useState([]);
  const [styleIds, setStyleIds] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const dropdownRef = useRef(null);
  const [description, setDescription] = useState("");
  const [shortDescription, setShortDescription] = useState("");
  const [orderBy, setOrderBy] = useState("");
  const [order, setOrder] = useState("asc");
  const [userData, setUserData] = useState(null);
  const [campaignSearchTerm, setCampaignSearchTerm] = useState("");
  const [showCampaignModal, setShowCampaignModal] = useState(false);
  const [selectedArticleForCampaigns, setSelectedArticleForCampaigns] =
    useState(null);
  const [articleCampaigns, setArticleCampaigns] = useState([]);
  const [campaignModalLoading, setCampaignModalLoading] = useState(false);
  const [showAddCampaignForm, setShowAddCampaignForm] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [showAddCampaignFormClick, setShowAddCampaignFormClick] =
    useState(false);
  const [showAddCampaignModal, setShowAddCampaignModal] = useState(false);

  const [campaignFormData, setCampaignFormData] = useState({
    budgetName: "",
    budgetAmountMicros: "",
    campaignName: "",
    customerId: "",
  });
  const [customerOptions, setCustomerOptions] = useState([]);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [selectedShowAds, setSelectedShowAds] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [showSecondRelatedSearch, setShowSecondRelatedSearch] = useState(false);

  const columns = [
    { id: "title", label: "Title" },
    ...(isSuperAdmin ? [{ id: "userName", label: "User Name" }] : []),
    { id: "categoryName", label: "Category" },
    { id: "showAds", label: "Show Ads" },
    { id: "customChannalName", label: "Custom Channel" },
    { id: "campaignCount", label: "Campaigns" },
    { id: "status", label: "Status" },
    { id: "domainName", label: "Domain Name" },
  ];

  const mapResponseToFrontend = (article) => {
    return {
      id: article.Id || article.id,
      title: article.Title || article.title || "No Title",
      url: article.Url || article.url || "",
      description: article.Description || article.description || "",
      shortDescription:
        article.ShortDescription || article.shortDescription || "",
      metatitle: article.MetaTitle || article.metatitle || "",
      metadescription: article.MetaDescription || article.metadescription || "",
      metakeys: article.MetaKeys || article.metakeys || "",
      hashtag: article.Hashtag || article.hashtag || "",
      customChannal:
        article.CustomChannalId &&
        (article.ChannelName || article.customChannalName)
          ? {
              Id: article.CustomChannalId,
              displayName: article.ChannelName || article.customChannalName,
              reportingDimensionId: `ga:${article.CustomChannalId}`,
            }
          : null,
      styleIdLm: article.StyleIdLm || "",
      styleIdDm: article.StyleIdDm || "",
      adrelatedsearches:
        article.AdRelatedSearches || article.adrelatedsearches || "7",
      adrelatedsearches2: article.AdReletadSearches2 || "", // Use API field name
      checkRelatedSearches2: !!article.CheckRelatedSearches2, // Map CheckRelatedSearches2
      remark: article.Remark || article.remark || "",
      published: !!article.Published || !!article.published,
      showArticle: !!article.ShowArticle || !!article.showArticle,
      showAds: !!article.ShowsAds || !!article.showsAds,
      image: article.Image || article.image || "",
      userId: article.User_Id_Settings || article.userId || "",
      userName: article.UserName || article.userName || "Unknown",
      category: article.CategoryName
        ? {
            Id: article.CategoryId || article.category?.Id,
            Name: article.CategoryName,
          }
        : null,
      domain: article.DomainName
        ? {
            Id: article.DomainId || article.domain?.Id,
            name: article.DomainName,
            showUrlName: article.DomainUrl,
          }
        : null,
      subdomain: article.SubDomainId || null,
      campaigns:
        article.Campaigns?.map((c) => ({
          SNo: c.SNo,
          Name: c.Name,
          Description: c.Description || "",
        })) || [],
      categoryName: article.CategoryName || "No category",
      showAds: article.ShowsAds || article.showsAds ? "True" : "False",
      customChannalName: article.CustomChannal || "No channel",
      channelId: article.CustomChannalId || "No ID",
      status: article.Published || article.published ? "Published" : "Draft",
      campaignCount: article.CampaignCount || 0,
      domainName: article.Domain,
    };
  };

  const defaultValuesForm = {
    title: { val: "", err: "" },
    category: { val: "", err: "" },
    url: { val: "", err: "" },
    description: { val: "", err: "" },
    shortDescription: { val: "", err: "" },
    metatitle: { val: "", err: "" },
    metadescription: { val: "", err: "" },
    metakeys: { val: "", err: "" },
    hashtag: { val: "", err: "" },
    customChannal: { val: "", err: "" },
    styleIdLm: { val: "", err: "" },
    styleIdDm: { val: "", err: "" },
    adrelatedsearches: { val: "7", err: "" },
    adrelatedsearches2: { val: "", err: "" },
    remark: { val: "", err: "" },
    campaigns: { val: [], err: "" },
    domain: { val: "", err: "" },
    subdomain: { val: "", err: "" },
    readTime: { val: "", err: "" },
  };
  const optionalFields = [
    "remark",
    "metatitle",
    "metadescription",
    "metakeys",
    "hashtag",
    "styleIdLm",
    "styleIdDm",
    "adrelatedsearches2", // Add new field
  ];
  const showAlert = (config) => {
    if (isMounted) {
      Swal.fire(config);
    }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const accessToken = localStorage.getItem("accessToken");
    if (accessToken) {
      try {
        const decoded = decodeJWT(accessToken);
        setUserData(decoded);
        setIsSuperAdmin(decoded.User_Type === "Super Admin" || decoded.User_Type === "Admin");
      } catch (error) {
        console.error("Error decoding JWT:", error);
        showAlert({
          icon: "error",
          title: "Authentication Error",
          text: "Invalid access token. Please log in again.",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    } else {
      showAlert({
        icon: "warning",
        title: "Session Expired",
        text: "Please log in to continue.",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, [isMounted]);
  const [formData, setFormData] = useState(defaultValuesForm);

  // const validateField = (name, value) => {
  //   if (optionalFields.includes(name)) {
  //     if (name === "adrelatedsearches2" && showSecondRelatedSearch) {
  //       return value ? "" : "2nd Ad Related Searches field is required when enabled.";
  //     }
  //     return "";
  //   }
  //   if (name === "readTime") {
  //     if (!value || isNaN(value) || parseInt(value) < 0) {
  //       return "Read Time must be a non-negative number.";
  //     }
  //     return "";
  //   }
  //   return value
  //     ? ""
  //     : `${name.replace(/([A-Z])/g, " $1").trim()} field is required.`;
  // };
  const validateField = (name, value, showAds) => {
    if (optionalFields.includes(name)) {
      if (name === "adrelatedsearches2" && showSecondRelatedSearch) {
        return value ? "" : "2nd Ad Related Searches field is required when enabled.";
      }
      return "";
    }
    if (name === "readTime") {
      if (!value || isNaN(value) || parseInt(value) < 0) {
        return "Read Time must be a non-negative number.";
      }
      return "";
    }
  
    // Only validate these fields when showAds is true
    const adRelatedFields = [
      "subdomain",
      "customChannal",
      "campaigns",
      "styleIdLm",
      "styleIdDm",
      "adrelatedsearches",
      "adrelatedsearches2",
    ];
    
    if (adRelatedFields.includes(name) && !showAds) {
      return "";
    }
  
    return value ? "" : `${name.replace(/([A-Z])/g, " $1").trim()} field is required.`;
  };
  const fetchArticles = useCallback(async () => {
    try {
      setShowLoader(true);
      let url = `/api/article/get?page=${page + 1}&length=${rowsPerPage}`;
      if (searchTerm) url += `&q=${encodeURIComponent(searchTerm)}`;
      if (selectedUser) url += `&userId=${encodeURIComponent(selectedUser)}`;
      if (selectedCategory) url += `&CategoryId=${encodeURIComponent(selectedCategory)}`;
      if (selectedDomain) url += `&DomainId=${encodeURIComponent(selectedDomain)}`;
      if (selectedSubdomain) url += `&SubDomainId=${encodeURIComponent(selectedSubdomain)}`;
      if (selectedShowAds !== "") url += `&showsAds=${encodeURIComponent(selectedShowAds)}`;
      if (selectedStatus !== "") url += `&published=${encodeURIComponent(selectedStatus)}`;
      if (orderBy) {
        const backendField = reverseFieldMapping[orderBy] || orderBy;
        url += `&orderBy=${encodeURIComponent(backendField)}&orderDir=${order}`;
      }

      const response = await axios.get(url, { withCredentials: true });
      const articlesData = response.data.data || [];
      const totalItems = response.data.pagination?.recordsFiltered || 0;

      const mappedArticles = articlesData.map(mapResponseToFrontend);
      setArticles(mappedArticles);
      setTotalCount(totalItems);
    } catch (error) {
      console.error("Error fetching articles:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch articles",
        timer: 3000,
        showConfirmButton: false,
      });
      setArticles([]);
      setTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [page, rowsPerPage, searchTerm, selectedUser, selectedCategory, selectedDomain, selectedSubdomain, selectedShowAds, selectedStatus, orderBy, order]);
  const fetchDomains = async () => {
    try {
      const response = await axios.get("/api/Domain/GetDropDown", {
        withCredentials: true,
      });
      setDomains(response.data.data);
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to load domains",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  };

  const fetchSubdomains = async () => {
    if (!selectedDomain) {
      setSubdomains([]);
      return;
    }
    try {
      const response = await axios.get(
        `/api/SubDomain/GetDropDown?DomainId=${selectedDomain}`,
        { withCredentials: true },
      );
      setSubdomains(response.data.data || []);
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to load subdomains",
          timer: 3000,
          showConfirmButton: false,
        });
      }
      setSubdomains([]);
      setSelectedSubdomain("");
    }
  };
  const fetchCategories = useCallback(async (params = {}) => {
    try {
      setShowLoader(true);
      const response = await axios.get("/api/category/GetDropdown", {
        params: { q: params.search || "" },
        withCredentials: true,
      });
      if (response.status === 200) {
        setCategories(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch categories",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  }, []);

  const fetchCampaigns = useCallback(async (subdomainId) => {
    if (!subdomainId) {
      setCampaigns([]);
      setSelectedCampaignIds([]);
      setFormData((prev) => ({
        ...prev,
        campaigns: { val: [], err: "" },
      }));
      return;
    }

    try {
      const response = await axios.get(
        `/api/Campaigns/GetDropdown?subdomainId=${subdomainId}`,
        {
          withCredentials: true,
        },
      );
      setCampaigns(response.data.data || []);
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch campaigns",
        timer: 3000,
        showConfirmButton: false,
      });
      setCampaigns([]);
    }
  }, []);

  const fetchStyleIds = useCallback(async () => {
    try {
      const response = await axios.get("/api/StyleIds/getdropdown", {
        withCredentials: true,
      });
      setStyleIds(response.data.data);
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch style IDs",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await axios.get("/api/adminuser/GetDropdown", {
        withCredentials: true,
      });
      setUsers(response.data.data);
    } catch (error) {
      showAlert({
        icon: "error",
        title: "Error",
        text: "Failed to fetch users",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  };

  const fetchChannels = useCallback(async () => {
    try {
      const response = await axios.get("/api/Channals/GetDropdown", {
        withCredentials: true,
      });
      setAssignChannels(response.data.data);
    } catch (error) {
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch channels",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const fetchCustomerOptions = useCallback(async (subDomainId = null) => {
    try {
      let url = "/api/AccountDetails/GetDropDown";
      if (subDomainId) {
        url = `/api/AccountDetails/GetAccountBySubDomain?subDomainId=${subDomainId}`;
      }

      const response = await axios.get(url, {
        withCredentials: true,
      });
      setCustomerOptions(response.data.data || []);
    } catch (error) {
      showAlert({
        icon: "error",
        title: "Error",
        text:
          error?.response?.data?.error || "Failed to fetch customer options",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const handleViewCampaignsClick = useCallback(
    async (rowData) => {
      const articleId = rowData?.id || rowData?.Id;
      const subdomainId = rowData?.subdomain|| rowData?.SubDomainId;

      if (!articleId) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: "Invalid article ID. Please ensure the article has a valid ID.",
          timer: 3000,
          showConfirmButton: false,
        });
        return;
      }

      if (!subdomainId) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: "No subdomain associated with this article.",
          timer: 3000,
          showConfirmButton: false,
        });
        return;
      }

      try {
        setCampaignModalLoading(true);
        setSelectedArticleForCampaigns(rowData);
        setShowCampaignModal(true);

        const [campaignsResponse, customerOptionsResponse] = await Promise.all([
          axios.get(`/api/Campaigns/GetData?articleId=${articleId}`, {
            withCredentials: true,
          }),
          axios.get(`/api/Campaigns/GetDropdown?subdomainId=${subdomainId}`, {
            withCredentials: true,
          }),
          fetchCustomerOptions(subdomainId),
        ]);
        setArticleCampaigns(campaignsResponse.data.data || []);
        setCampaigns(customerOptionsResponse.data.data || []);
      } catch (error) {
        console.error("Error fetching data:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch data",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setCampaignModalLoading(false);
      }
    },
    [fetchCustomerOptions],
  );
  const handleAddCampaign = useCallback(
    async (e) => {
      e.preventDefault();

      try {
        setCampaignModalLoading(true);

        await axios.post("/api/Campaigns/Add", campaignFormData, {
          withCredentials: true,
        });

        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Campaign created successfully",
          timer: 2000,
          showConfirmButton: false,
        });

        setCampaignFormData({
          budgetName: "",
          budgetAmountMicros: "",
          campaignName: "",
          customerId: "",
        });
        setShowAddCampaignForm(false);
        setShowAddCampaignFormClick(false);

        if (selectedArticleForCampaigns) {
          await handleViewCampaignsClick(selectedArticleForCampaigns);
        }
      } catch (error) {
        console.error("Error creating campaign:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to create campaign",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setCampaignModalLoading(false);
      }
    },
    [campaignFormData, selectedArticleForCampaigns, handleViewCampaignsClick],
  );

  
  const handleAssignCampaign = async (e) => {
    e.preventDefault();
  
    if (
      !campaignFormData.campaignId ||
      !selectedArticleForCampaigns?.id
    ) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Please select a campaign and ensure an article is selected.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }
  
    try {
      setCampaignModalLoading(true);
  
      const res = await axios.post(
        `/api/Campaigns/ArticleCampaignMappings?articleId=${selectedArticleForCampaigns.id}&campaignId=${Number(campaignFormData.campaignId)}`,
        {},
        { withCredentials: true },
      );
      
      if (res.data.message) {
        Swal.fire({
          icon: "warning",
          title: "Success",
          text: `${res.data.message}`,
          timer: 2000,
          showConfirmButton: false,
        });
      }else{
        Swal.fire({
          icon: "success",
          title: "Success",
          text: "Campaign assigned successfully",
          timer: 2000,
          showConfirmButton: false,
        });
      }
      await fetchArticles();
      await handleViewCampaignsClick(selectedArticleForCampaigns);
    } catch (error) {
      console.error("Error assigning campaign:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text:
          error?.response?.data?.error || "Failed to assign campaign",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setCampaignModalLoading(false);
    }
  };
  

  const handleCampaignFormChange = useCallback((e) => {
    const { name, value } = e.target;
    setCampaignFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handleRemoveCampaign = useCallback(
    async (campaignData) => {
      if (!campaignData?.Id) {
        showAlert({
          icon: "error",
          title: "Error",
          text: "Invalid campaign data",
          timer: 3000,
          showConfirmButton: false,
        });
        return;
      }

      try {
        const result = await Swal.fire({
          icon: "warning",
          title: "Confirm Removal",
          text: `Are you sure you want to remove campaign "${campaignData.Name}"?`,
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: "Yes, remove it!",
          cancelButtonText: "Cancel",
        });

        if (result.isConfirmed) {
          setCampaignModalLoading(true);

          await axios.delete("/api/Campaigns/Delete", {
            data: { Id: campaignData.Id },
            withCredentials: true,
            headers: { "Content-Type": "application/json" },
          });

          showAlert({
            icon: "success",
            title: "Success",
            text: "Campaign removed successfully",
            timer: 2000,
            showConfirmButton: false,
          });
          fetchArticles();
          if (selectedArticleForCampaigns) {
            await handleViewCampaignsClick(selectedArticleForCampaigns);
          }
        }
      } catch (error) {
        console.error("Error removing campaign:", error);
        showAlert({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to remove campaign",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setCampaignModalLoading(false);
      }
    },
    [selectedArticleForCampaigns, handleViewCampaignsClick],
  );

  const handleFormSubmit = async (e) => {
    e.preventDefault();

    const updatedFormData = {
      ...formData,
      title: {
        val: formData.title.val,
        err: validateField("title", formData.title.val, showAds),
      },
      category: {
        val: formData.category.val,
        err: validateField("category", formData.category.val, showAds),
      },
      description: {
        val: description,
        err: validateField("description", description, showAds),
      },
      shortDescription: {
        val: shortDescription,
        err: validateField("shortDescription", shortDescription, showAds),
      },
      domain: {
        val: selectedDomain,
        err: validateField("domain", selectedDomain, showAds),
      },
      subdomain: {
        val: selectedSubdomain,
        err: validateField("subdomain", selectedSubdomain, showAds),
      },
      customChannal: {
        val: formData.customChannal.val,
        err: validateField("customChannal", formData.customChannal.val, showAds),
      },
      styleIdLm: {
        val: formData.styleIdLm.val,
        err: validateField("styleIdLm", formData.styleIdLm.val, showAds),
      },
      styleIdDm: {
        val: formData.styleIdDm.val,
        err: validateField("styleIdDm", formData.styleIdDm.val, showAds),
      },
      adrelatedsearches: {
        val: formData.adrelatedsearches.val,
        err: validateField("adrelatedsearches", formData.adrelatedsearches.val, showAds),
      },
      adrelatedsearches2: {
        val: formData.adrelatedsearches2?.val || "",
        err: validateField("adrelatedsearches2", formData.adrelatedsearches2?.val || "", showAds),
      },
      readTime: {
        val: formData.readTime.val,
        err: validateField("readTime", formData.readTime.val, showAds),
      },
    };
  
    const hasErrors = Object.values(updatedFormData).some((field) => field.err);
    if (hasErrors) {
      setFormData(updatedFormData);
      await Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Please fill all required fields",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }
  
    // Additional validation for CheckRelatedSearches2
    if (formData.adrelatedsearches2?.val && !showSecondRelatedSearch) {
      await Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "CheckRelatedSearches2 must be true if AdRelatedSearches2 is provided",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }
  
    setShowLoader(true);
    try {
      const formDataPayload = new FormData();
  
      formDataPayload.append("Title", formData.title.val);
      formDataPayload.append("Category", formData.category.val);
  
      const urlSlug =
        formData.url.val.split("/").pop() || autoslug(formData.title.val);
      formDataPayload.append("Url", urlSlug);
      formDataPayload.append("MetaTitle", formData.metatitle.val || "");
      formDataPayload.append("MetaDescription", formData.metadescription.val || "");
      formDataPayload.append("MetaKeys", formData.metakeys.val || "");
      if (description) formDataPayload.append("Description", description);
      if (shortDescription)
        formDataPayload.append("ShortDescription", shortDescription);
      if (formData.customChannal.val)
        formDataPayload.append("CustomChannal", formData.customChannal.val);
      if (showAds && formData.styleIdLm.val) {
        formDataPayload.append("StyleIdLm", formData.styleIdLm.val);
      }
      if (showAds && formData.styleIdDm.val) {
        formDataPayload.append("StyleIdDm", formData.styleIdDm.val);
      }
      if (showAds && formData.adrelatedsearches?.val) formDataPayload.append("AdRelatedSearches", formData.adrelatedsearches.val);
      
      if (showAds && formData.adrelatedsearches2?.val)
        formDataPayload.append("AdReletadSearches2", formData.adrelatedsearches2.val); // Use API field name
      if (formData.remark.val)
        formDataPayload.append("Remark", formData.remark.val);
      if (formData.hashtag.val)
        formDataPayload.append("Hashtag", formData.hashtag.val);
  
      formDataPayload.append("Published", String(published));
      formDataPayload.append("ShowArticle", String(showArticle));
      formDataPayload.append("ShowsAds", String(showAds));
      formDataPayload.append("CheckRelatedSearches2", String(showSecondRelatedSearch));
  
      if (selectedDomain) formDataPayload.append("Domain", selectedDomain);
      if (selectedSubdomain)
        formDataPayload.append("SubDomain", selectedSubdomain);
  
      if (formdataImage && typeof formdataImage === "object") {
        formDataPayload.append("file", formdataImage);
      }
  
      if (editId) {
        formDataPayload.append("Id", editId);
      }
  
      const numericCampaignIds = selectedCampaignIds.map(id => Number(id));
      formDataPayload.append("CampaignIds", JSON.stringify(numericCampaignIds));
  
      const endpoint = editId ? "/api/article/edit" : "/api/article/add";
      const method = editId ? "PUT" : "POST";
  
      const response = await axios({
        url: endpoint,
        method,
        data: formDataPayload,
        headers: { "Content-Type": "multipart/form-data" },
        withCredentials: true,
      });
  
      if ([200, 201].includes(response.status)) {
        if (editId) {
          try {
            // Find domain and subdomain data
            const domain = domains.find(d => d.Id === selectedDomain);
            const subdomain = subdomains.find(sd => sd.Id === selectedSubdomain);
  
            if (domain && subdomain) {
              console.log(domain, subdomain);
              
              const domainName = domain.ShowUrlName;
              const subdomainName = subdomain.Url;
              const showUrlName = formData.url.val.split("/").pop() || autoslug(formData.title.val);
              
              const cacheInvalidationUrl = `https://${subdomainName}.${domainName}/cache/invalidate/${showUrlName}`;
              console.log("Cache invalidation URL:", cacheInvalidationUrl);
  
              // Make the GET request to invalidate cache
              const invalidationResponse = await axios.get(cacheInvalidationUrl);
              console.log("Cache invalidation response:", invalidationResponse.data);
            }
          } catch (cacheError) {
            console.error("Cache invalidation failed:", cacheError);
            // Don't block success flow - just show warning
            await Swal.fire({
              icon: "warning",
              title: "Cache Not Cleared",
              text: "Article updated but cache invalidation failed",
              timer: 3000,
              showConfirmButton: false,
            });
          }
        }
        resetFormState();
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: editId
            ? "Article updated successfully"
            : "Article created successfully",
          timer: 3000,
          showConfirmButton: false,
        });
        fetchArticles();
        setModalShow(false);
      }
    } catch (error) {
      console.error("Error saving article:", error.response?.data || error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to save article",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };
  const filteredCampaigns = campaignSearchTerm
    ? articleCampaigns.filter((campaign) =>
        campaign.Name.toLowerCase().includes(campaignSearchTerm.toLowerCase()),
      )
    : articleCampaigns;

  const resetFormState = () => {
    setEditId(null);
    setFormData(defaultValuesForm);
    setDescription("");
    setShortDescription("");
    setFormdataImage("");
    setBase64Image("");
    setPublished(true);
    setShowArticle(false);
    setShowAds(false);
    setSelectedDomain("");
    setSelectedSubdomain("");
    setSelectedCampaignIds([]);
    // setSearchTerm("");
    setEditSlugMode(false);
  };

  const resetFormStateForClone = () => {
    setEditId(null);
    setSelectedDomain("");
    setSelectedSubdomain("");
    setSelectedCampaignIds([]);
    setSubdomains([]);
    setCampaigns([]);
    setEditSlugMode(false);
  };

  const handleChange = (e) => {
    const { name, value, files } = e.target || e;
    if (name === "title") {
      const shouldUpdateSlug = !editId || editSlugMode;
      setFormData((prev) => ({
        ...prev,
        [name]: { val: value, err: validateField(name, value) },
        ...(shouldUpdateSlug && { url: { val: autoslug(value), err: "" } }),
      }));
      return;
    }
    if (name === "url") {
      setFormData((prev) => ({
        ...prev,
        url: { val: autoslug(value), err: "" },
      }));
      return;
    }
    if (files && files.length > 0) {
      setFormdataImage(files[0]);
      setBase64Image("");
      return;
    }
    setFormData((prev) => ({
      ...prev,
      [name]: { val: value, err: validateField(name, value) },
    }));
  };

  const handleCampaignSelect = (campaignIds) => {
    setSelectedCampaignIds(campaignIds);
    setFormData((prev) => ({
      ...prev,
      campaigns: { val: campaignIds, err: "" },
    }));
  };

  const handleDescriptionChange = (newContent) => {
    setDescription(newContent);
    setFormData((prev) => ({
      ...prev,
      description: {
        val: newContent,
        err: validateField("description", newContent),
      },
    }));
  };

  const handleShortDescriptionChange = (newContent) => {
    setShortDescription(newContent);
    setFormData((prev) => ({
      ...prev,
      shortDescription: {
        val: newContent,
        err: validateField("shortDescription", newContent),
      },
    }));
  };

  const handleChannelSelect = (channel) => {
    setFormData((prev) => ({
      ...prev,
      customChannal: {
        val: channel?.Id || "",
        err: validateField("customChannal", channel?.Id || ""),
      },
    }));
  };

  const handleEditArticle = useCallback(
    async (rowData) => {
      try {
        const loadPromises = [];
  
        if (assignChannels.length === 0) {
          loadPromises.push(fetchChannels());
        }
        if (domains.length === 0) {
          loadPromises.push(fetchDomains());
        }
        if (styleIds.length === 0) {
          loadPromises.push(fetchStyleIds());
        }
  
        await Promise.all(loadPromises);
  
        const response = await axios.get(
          `/api/article/GetById?id=${rowData.id}`,
          {
            withCredentials: true,
          },
        );
  
        if (response.data.success && response.data.data?.length > 0) {
          const article = response.data.data[0];
          const showAds = !!article.ShowsAds;
          setModalShow(true);
          setEditId(article.Id);
          setEditSlugMode(false);
  
          const channelId = article.ChannelId || "";
  
          setPublished(!!article.Published);
          setShowArticle(!!article.ShowArticle);
          setShowAds(!!article.ShowsAds);
          setShowSecondRelatedSearch(!!article.CheckRelatedSearches2); // Set based on API response
  
          setDescription(article.Description || "");
          setShortDescription(article.ShortDescription || "");
  
          const formDataToSet = {
            ...defaultValuesForm,
            title: { val: article.Title || "", err: "" },
            category: { val: article.CategoryId || "", err: "" },
            url: { val: article.Url || "", err: "" },
            description: { val: article.Description || "", err: "" },
            shortDescription: { val: article.ShortDescription || "", err: "" },
            metatitle: { val: article.MetaTitle || "", err: "" },
            metadescription: { val: article.MetaDescription || "", err: "" },
            metakeys: { val: article.MetaKeys || "", err: "" },
            hashtag: { val: article.Hashtag || "", err: "" },
            customChannal: { val: channelId, err: "" },
            styleIdLm: { val: article.StyleIdLm || "", err: showAds ? validateField("styleIdLm", article.StyleIdLm || "", showAds) : "" },
            styleIdDm: { val: article.StyleIdDm || "", err: showAds ? validateField("styleIdDm", article.StyleIdDm || "", showAds) : "" },
            adrelatedsearches: { val: article.AdRelatedSearches, err: "" },
            adrelatedsearches2: { val: article.AdReletadSearches2 || "", err: "" }, // Handle null
            remark: { val: article.Remark || "", err: "" },
            campaigns: {
              val: article.Campaigns?.map((c) => c.SNo) || [],
              err: "",
            },
            domain: { val: article.DomainId || "", err: "" },
            subdomain: { val: article.SubDomainId || "", err: "" },
            readTime: { val: article.ReadTime?.toString() || "", err: "" },
          };
  
          setFormData({...formDataToSet, 
            styleIdType: {
              val: article.StyleIdType || "LmStyleId", // Default to Dark Mode
              err: ""
            },
            styleId: {
              val: article.StyleId || "",
              err: ""
            },
            subdomain: {
            val: article.SubDomainId || "",
            err: showAds ? validateField("subdomain", article.SubDomainId || "", showAds) : "",
          },
          customChannal: {
            val: article.ChannelId || "",
            err: showAds ? validateField("customChannal", article.ChannelId || "", showAds) : "",
          },});
  
          setSelectedDomain(article.DomainId || "");
  
          if (article.DomainId) {
            try {
              const subdomainResponse = await axios.get(
                `/api/SubDomain/GetDropDown?DomainId=${article.DomainId}`,
                { withCredentials: true },
              );
              setSubdomains(subdomainResponse.data.data || []);
              setSelectedSubdomain(article.SubDomainId || "");
            } catch (error) {
              console.error("Error loading subdomains:", error);
              setSubdomains([]);
              setSelectedSubdomain("");
            }
          } else {
            setSelectedSubdomain("");
          }
  
          if (article.SubDomainId) {
            await fetchCampaigns(article.SubDomainId);
            if (article.Campaigns && article.Campaigns.length > 0) {
              const campaignIds = article.Campaigns.map((c) =>
                c.SNo.toString()
              );
              setSelectedCampaignIds(campaignIds);
            }
          } else {
            setSelectedCampaignIds([]);
          }
  
          if (article.Image) {
            setFormdataImage(
              fetchImage(
                article.Image.includes("cloudinary") ? "cloud" : "small",
                article.Image,
              ),
            );
          } else {
            setFormdataImage("");
          }
          setBase64Image("");
        }
        await fetchArticles();
      } catch (error) {
        console.error("Error fetching article:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: "Failed to load article data",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setShowLoader(false);
      }
    },
    [
      fetchCampaigns,
      assignChannels,
      domains,
      styleIds,
      fetchChannels,
      fetchDomains,
      fetchStyleIds,
    ],
  );
  const handleCloneArticle = useCallback(
    async (rowData) => {
      try {
        const loadPromises = [];
        if (assignChannels.length === 0) {
          loadPromises.push(fetchChannels());
        }
        if (domains.length === 0) {
          loadPromises.push(fetchDomains());
        }
        if (styleIds.length === 0) {
          loadPromises.push(fetchStyleIds());
        }
  
        await Promise.all(loadPromises);
  
        const response = await axios.get(`/api/article/GetById?id=${rowData.id}`, {
          withCredentials: true,
        });
  
        if (response.data.success && response.data.data?.length > 0) {
          const article = response.data.data[0];
          const showAds = !!article.ShowsAds;

          setModalShow(true);
          setEditId(null);
          setEditSlugMode(false);
  
          setPublished(!!article.Published);
          setShowArticle(!!article.ShowArticle);
          setShowAds(!!article.ShowsAds);
          setShowSecondRelatedSearch(!!article.CheckRelatedSearches2); // Set based on API response
  
          setDescription(article.Description || "");
          setShortDescription(article.ShortDescription || "");
  
          const formDataToSet = {
            ...defaultValuesForm,
            title: { val: `${article.Title || ""}`, err: "" },
            category: { val: article.CategoryId || "", err: "" },
            url: { val: `${article.Url || ""}`, err: "" },
            description: { val: article.Description || "", err: "" },
            shortDescription: { val: article.ShortDescription || "", err: "" },
            metatitle: { val: article.MetaTitle || "", err: "" },
            metadescription: { val: article.MetaDescription || "", err: "" },
            metakeys: { val: article.MetaKeys || "", err: "" },
            hashtag: { val: article.Hashtag || "", err: "" },
            customChannal: { val: "", err: "" },
            styleIdLm: { val: "", err: "" },
            styleIdDm: { val: "", err: "" },
            adrelatedsearches: { val: article.AdRelatedSearches || "7", err: "" },
            adrelatedsearches2: { val: article.AdReletadSearches2 || "", err: "" }, // Handle null
            remark: { val: article.Remark || "", err: "" },
            campaigns: { val: [], err: "" },
            domain: { val: "", err: "" },
            subdomain: { val: "", err: "" },
            readTime: { val: article.ReadTime?.toString() || "", err: "" },
          };
  
          setFormData({...formDataToSet,
            styleIdType: {
              val: article.StyleIdType || "LmStyleId",
              err: ""
            },
            styleId: {
              val: article.StyleId || "",
              err: ""
            },
            subdomain: {
            val: "",
            err: "",
          },
          customChannal: {
            val: "",
            err: "",
          },});
  
          setSelectedDomain("");
          setSelectedSubdomain("");
          setSelectedCampaignIds([]);
          setSubdomains([]);
          setCampaigns([]);
  
          if (article.Image) {
            setFormdataImage(
              fetchImage(
                article.Image.includes("cloudinary") ? "cloud" : "small",
                article.Image,
              ),
            );
          } else {
            setFormdataImage("");
          }
          setBase64Image("");
        }
      } catch (error) {
        console.error("Error cloning article:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: "Failed to clone article data",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setShowLoader(false);
      }
    },
    [
      assignChannels,
      domains,
      styleIds,
      fetchChannels,
      fetchDomains,
      fetchStyleIds,
      defaultValuesForm,
    ],
  );
  const handleDeleteArticle = useCallback(
    async (rowData) => {
      if (typeof window !== "undefined") {
        const result = await Swal.fire({
          icon: "warning",
          title: "Confirm Deletion",
          text: "Are you sure you want to delete this article?",
          showCancelButton: true,
          confirmButtonColor: "#5750f1",
          cancelButtonColor: "#d33",
          showCloseButton: true,
          confirmButtonText: "Yes, delete it!",
        });

        if (result.isConfirmed) {
          try {
            setShowLoader(true);
            await axios.delete("/api/article/delete", {
              data: { id: rowData.id },
              withCredentials: true,
              headers: { "Content-Type": "application/json" },
            });
            if (typeof window !== "undefined") {
              await Swal.fire({
                icon: "success",
                title: "Success",
                text: "Article deleted successfully",
                timer: 3000,
                showConfirmButton: false,
              });
            }
            fetchArticles();
          } catch (error) {
            console.error("Error deleting article:", error);
            if (typeof window !== "undefined") {
              await Swal.fire({
                icon: "error",
                title: "Error",
                text:
                  error?.response?.data?.error || "Failed to delete article",
                timer: 3000,
                showConfirmButton: false,
              });
            }
          } finally {
            setShowLoader(false);
          }
        }
      }
    },
    [fetchArticles],
  );

  const handleAddCampaignClick = useCallback(
    async (rowData) => {
      const articleId = rowData?.id || rowData?.Id;

      if (!articleId) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: "Invalid article ID. Please ensure the article has a valid ID.",
          timer: 3000,
          showConfirmButton: false,
        });
        return;
      }

      try {
        setCampaignModalLoading(true);
        setSelectedArticleForCampaigns(rowData);
        setShowAddCampaignModal(true);

        const [campaignsResponse] = await Promise.all([
          axios.get(`/api/Campaigns/GetData?articleId=${articleId}`, {
            withCredentials: true,
          }),
          fetchCustomerOptions(rowData?.subdomain?.Id || rowData?.SubDomainId),
        ]);

        setArticleCampaigns(campaignsResponse.data.data || []);
      } catch (error) {
        console.error("Error fetching article campaigns:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch campaigns",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setCampaignModalLoading(false);
      }
    },
    [fetchCustomerOptions],
  );

  const handleViewArticle = useCallback((rowData) => {
    if (!rowData.published) {
      showAlert({
        icon: "warning",
        title: "Warning",
        text: "Article is not published yet",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }
    const link = makeLinkRedirect(rowData);
    window.open(link, "_blank");
  }, []);

  const makeLinkRedirect = (article) => {
    if (!article.published) return "/";
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE;

    if (article.domainName) {
      const slug = article.url.split("/").pop();
      return `https://${article.domainName}/${slug}`;
    }

    const channelId =
      article.customChannal?.reportingDimensionId?.split(":")[1] || "";
    return `${baseUrl}/${article.url.split("/").pop()}?channel=${channelId}&mode=light`;
  };

  const autoslug = (title) =>
    
    title ? title.replace(/[^\p{L}\p{N}\s-]/gu, '') // Remove symbols except letters, numbers, hyphens, and spaces
    .trim()
    .replace(/\s+/g, '-').toLowerCase() : "";

  const imageConvert = async () => {
    if (formdataImage && typeof formdataImage === "object") {
      try {
        const base64 = await convertToBase64(formdataImage);
        setBase64Image(base64);
      } catch (error) {
        console.error("Failed to convert image:", error);
        setBase64Image("");
        showAlert({
          icon: "error",
          title: "Error",
          text: "Failed to process image. Please select a valid image file.",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  };

  const renderSlugField = () => (
    <div className="mb-4">
      <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
        URL
      </label>
      <div className="flex flex-wrap items-center gap-2">
        <span className="rounded-md border border-stroke bg-gray-100 px-4 py-3 text-dark dark:border-dark-3 dark:bg-dark-2 dark:text-white">
          {selectedDomain
            ? `https://${
                selectedSubdomain
                  ? `${subdomains.find((s) => s.Id === selectedSubdomain)?.Url || ""}.`
                  : ""
              }${domains.find((d) => d.Id === selectedDomain)?.ShowUrlName || ""}/`
            : `${process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE}/`}
        </span>
        <input
          type="text"
          name="url"
          value={formData.url.val.split("/").pop()}
          onChange={handleChange}
          className="min-w-[200px] flex-1 rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20 dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
          readOnly={!editSlugMode}
        />
        <label className="flex items-center gap-2">
          <Checkbox
            label="Edit Slug"
            name="editSlug"
            checked={editSlugMode}
            onChange={() => setEditSlugMode(!editSlugMode)}
            withIcon="check"
            withBg
            radius="default"
          />
        </label>
      </div>
      {formData.url.err && (
        <p className="mt-1 text-sm text-red-500">{formData.url.err}</p>
      )}
    </div>
  );

  useEffect(() => {
    fetchCategories();
    fetchArticles();
  }, [fetchArticles]);
  useEffect(() => {
    isSuperAdmin && fetchUsers();
  }, [isSuperAdmin]);

  useEffect(() => {
    if (modalShow) {
      fetchChannels();
      fetchStyleIds();
      fetchDomains();
    }
  }, [modalShow, fetchChannels, fetchStyleIds]);

  useEffect(() => {
    fetchSubdomains();
  }, [selectedDomain]);

  useEffect(() => {
    if (selectedSubdomain) {
      fetchCampaigns(selectedSubdomain);
    } else {
      setCampaigns([]);
      setSelectedCampaignIds([]);
      setFormData((prev) => ({
        ...prev,
        campaigns: { val: [], err: "" },
      }));
    }
  }, [selectedSubdomain, fetchCampaigns]);

  useEffect(() => {
    imageConvert();
  }, [formdataImage]);
  useEffect(() => {
    fetchCategories();
    fetchArticles();
    fetchDomains();
  }, [fetchArticles, fetchCategories]);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);
  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white">
              Articles Management
            </h1>
          </div>
        </div>

        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
          <div className="mb-5 flex flex-col flex-wrap gap-4">
            <div className="flex items-center justify-between gap-4">
              <div className="flex flex-wrap items-center gap-4">
                <div className="w-full mt-4 sm:w-64">
                  <Button
                    type="button"
                    label="Add Article"
                    variant="primary"
                    shape="rounded"
                    className="flex w-full items-center justify-center gap-2 sm:w-64"
                    icon={<FaPlus size={14} />}
                    onClick={() => {
                      setModalShow(true);
                      resetFormState();
                    }}
                  />
                </div>
                <div className="w-full  sm:w-64">
                  <InputGroup
                    label="Search"
                    placeholder="Search..."
                    value={searchTerm}
                    handleChange={(e) => {
                      setSearchTerm(e.target.value);
                      setPage(0);
                    }}
                    type="text"
                    className="w-full"
                  />
                </div>
                <div className="w-full z-40 sm:w-64">
                  <SearchableDropdown
                    options={[
                      { Id: "", Name: "All Categories" },
                      ...categories,
                    ]}
                    label="Categories"
                    placeholder="Select Category..."
                    value={selectedCategory}
                    onChange={(category) => {
                      setSelectedCategory(category?.Id || "");
                      setPage(0);
                    }}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div>
                {isSuperAdmin && (
                  <div className="w-full z-70 sm:w-64">
                    <SearchableDropdown
                      options={[{ Id: "", Name: "All Users" }, ...users]}
                      label="Users"
                      placeholder="Select User..."
                      value={selectedUser}
                      onChange={(user) => {
                        setSelectedUser(user?.Id || "");
                        setPage(0);
                      }}
                      displayKey="Name"
                      idKey="Id"
                    />
                  </div>
                )}
              </div>
              <IconButton
                onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                className="hover:bg-primary-dark flex items-center justify-center rounded-md bg-primary p-2 text-white"
                aria-label={
                  isFilterExpanded ? "Collapse Filters" : "Expand Filters"
                }
              >
                <FaFilter size={14} />
              </IconButton>
            </div>
            {isFilterExpanded && (
              <div className="flex z-1 flex-wrap items-center gap-4">
                <div className="w-full z-40 sm:w-64">
                  <SearchableDropdown
                    label="Domain"
                    options={[{ Id: "", Name: "All Domains" }, ...domains]}
                    placeholder="Select Domain..."
                    value={selectedDomain}
                    onChange={(domain) => {
                      setSelectedDomain(domain?.Id || "");
                      setSelectedSubdomain("");
                      setPage(0);
                    }}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div>
                <div className="w-full z-30 sm:w-64">
                  <SearchableDropdown
                    label="Subdomain"
                    options={[
                      { Id: "", Name: "All Subdomains" },
                      ...subdomains,
                    ]}
                    placeholder="Select Subdomain..."
                    value={selectedSubdomain}
                    onChange={(subdomain) => {
                      setSelectedSubdomain(subdomain?.Id || "");
                      setPage(0);
                    }}
                    displayKey="Name"
                    idKey="Id"
                    disabled={!selectedDomain || !subdomains.length}
                  />
                </div>
                <div className="w-full z-20 sm:w-64">
                  <SearchableDropdown
                    label="Show Ads"
                    options={[
                      { Id: "-1", Name: "All" },
                      { Id: "0", Name: "True" },
                      { Id: "1", Name: "False" },
                    ]}
                    placeholder="Select Show Ads..."
                    value={selectedShowAds}
                    onChange={(option) => {
                      setSelectedShowAds(option?.Id || "");
                      setPage(0);
                    }}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div>
                <div className="w-full sm:w-64">
                  <SearchableDropdown
                    label="Status"
                    options={[
                      { Id: "-1", Name: "All" },
                      { Id: "0", Name: "Published" },
                      { Id: "1", Name: "Draft" },
                    ]}
                    placeholder="Select Status..."
                    value={selectedStatus}
                    onChange={(option) => {
                      setSelectedStatus(option?.Id || "");
                      setPage(0);
                    }}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div>
              </div>
            )}
          </div>
          <CustomDataTable
            isLoading={showLoader}
            columns={columns}
            rows={articles}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={setPage}
            onRequestSort={(_, property) => {
              const isAsc = orderBy === property && order === "asc";
              setOrder(isAsc ? "desc" : "asc");
              setOrderBy(property);
            }}
            onRowsPerPageChange={(value) => {
              setRowsPerPage(value);
              setPage(0);
            }}
            totalCount={totalCount}
            order={order}
            orderBy={orderBy}
            onView={handleViewArticle}
            onEdit={handleEditArticle}
            onClone={handleCloneArticle}
            onDelete={handleDeleteArticle}
            handleViewCampaignsClick={handleViewCampaignsClick}
            handleAddCampaignClick={handleAddCampaignClick}
          />
        </div>
      </div>

      <ArticleModal
        open={modalShow}
        onClose={() => {
          setModalShow(false);
          resetFormState();
        }}
        mode={editId ? "edit" : "add"}
        formData={formData}
        setFormData={setFormData}
        description={description}
        setDescription={setDescription}
        shortDescription={shortDescription}
        setShortDescription={setShortDescription}
        formdataImage={formdataImage}
        setFormdataImage={setFormdataImage}
        base64Image={base64Image}
        setBase64Image={setBase64Image}
        published={published}
        setPublished={setPublished}
        showArticle={showArticle}
        setShowArticle={setShowArticle}
        showAds={showAds}
        setShowAds={setShowAds}
        selectedDomain={selectedDomain}
        setSelectedDomain={setSelectedDomain}
        selectedSubdomain={selectedSubdomain}
        setSelectedSubdomain={setSelectedSubdomain}
        selectedCampaignIds={selectedCampaignIds}
        setSelectedCampaignIds={setSelectedCampaignIds}
        domains={domains}
        subdomains={subdomains}
        categories={categories}
        assignChannels={assignChannels}
        styleIds={styleIds}
        campaigns={campaigns}
        editId={editId}
        editSlugMode={editSlugMode}
        setEditSlugMode={setEditSlugMode}
        handleFormSubmit={handleFormSubmit}
        handleChange={handleChange}
        handleChannelSelect={handleChannelSelect}
        handleCampaignSelect={handleCampaignSelect}
        validateField={validateField}
        autoslug={autoslug}
        showLoader={showLoader}
        showSecondRelatedSearch={showSecondRelatedSearch}
        setShowSecondRelatedSearch={setShowSecondRelatedSearch}
      />
      <Dialog
        open={showCampaignModal}
        onClose={() => {
          setShowCampaignModal(false);
          setSelectedArticleForCampaigns(null);
          setArticleCampaigns([]);
          setShowAddCampaignForm(false);
          setShowAddCampaignFormClick(false);
          setCampaignSearchTerm("");
        }}
        fullWidth
        maxWidth="md"
        slotProps={{
          paper: {
            sx: {
              maxHeight: "90vh",
              zIndex: 1300,
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span className="text-xl font-medium">
            Campaigns for{" "}
            <span className="font-bold">
              "{selectedArticleForCampaigns?.title}"
            </span>
          </span>
          <IconButton
            aria-label="close"
            onClick={() => {
              setShowCampaignModal(false);
              setSelectedArticleForCampaigns(null);
              setArticleCampaigns([]);
              setShowAddCampaignForm(false);
              setShowAddCampaignFormClick(false);
              setCampaignSearchTerm("");
            }}
            sx={{
              color: "white",
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          <>
            <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                <div className="min-w-72.5">
                  <SearchableDropdown
                    label="Select Campaign"
                    className="overflow-hidden"
                    options={campaigns}
                    placeholder="Select a campaign..."
                    value={campaignFormData.campaignId || ""}
                    onChange={(campaign) => {
                      setCampaignFormData((prev) => ({
                        ...prev,
                        campaignId: campaign?.SNo || "",
                      }));
                    }}
                    displayKey="Name"
                    idKey="SNo"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  className="mt-4"
                  label={"Assign Campaign"}
                  variant="primary"
                  shape="rounded"
                  onClick={handleAssignCampaign}
                />
              </div>
            </div>

            <CustomDataTable
              isLoading={campaignModalLoading}
              columns={[
                { id: "CampaignId", label: "Campaign ID" },
                { id: "Name", label: "Campaign Name" },
                { id: "CreatedAt", label: "Added Date" },
              ]}
              rows={filteredCampaigns}
              page={0}
              rowsPerPage={10}
              onPageChange={() => {}}
              onRowsPerPageChange={() => {}}
              totalCount={articleCampaigns.length}
              searchTerm={campaignSearchTerm}
              onSearchChange={setCampaignSearchTerm}
              order="asc"
              orderBy=""
              onRequestSort={() => {}}
              onDelete={handleRemoveCampaign}
              showDeleteAction={true}
            />
          </>
        </DialogContent>

        <DialogActions sx={{ py: 2, px: 3 }}>
          <Button
            type="button"
            label="Cancel"
            onClick={() => {
              setShowCampaignModal(false);
              setSelectedArticleForCampaigns(null);
              setArticleCampaigns([]);
              setCampaignSearchTerm("");
              setCampaignFormData({
                budgetName: "",
                budgetAmountMicros: "",
                campaignName: "",
                customerId: "",
                campaignId: "", // Reset campaignId
              });
            }}
            variant="dark"
            shape="rounded"
          />
        </DialogActions>
      </Dialog>

      <Dialog
        open={showAddCampaignModal}
        onClose={() => {
          setShowAddCampaignModal(false);
          setSelectedArticleForCampaigns(null);
          setArticleCampaigns([]);
          setShowAddCampaignFormClick(false);
          setCampaignSearchTerm("");
        }}
        fullWidth
        maxWidth="sm"
        slotProps={{
          paper: {
            sx: {
              maxHeight: "90vh",
              // zIndex: 1300,
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span className="text-xl font-medium">
            Campaigns for{" "}
            <span className="font-bold">
              "{selectedArticleForCampaigns?.title}"
            </span>
          </span>
          <IconButton
            aria-label="close"
            onClick={() => {
              setShowAddCampaignModal(false);
              setSelectedArticleForCampaigns(null);
              setArticleCampaigns([]);
              setShowAddCampaignFormClick(false);
              setCampaignSearchTerm("");
            }}
            sx={{
              color: "white",
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          <form onSubmit={handleAddCampaign}>
            <h3 className="mb-4 text-lg font-semibold">Add New Campaign</h3>

            <div className="mb-4">
              <SearchableDropdown
                label="Customer ID"
                options={customerOptions}
                placeholder="Select Customer..."
                value={campaignFormData.customerId}
                onChange={(customer) => {
                  setCampaignFormData((prev) => ({
                    ...prev,
                    customerId: customer?.AccountId || "",
                  }));
                }}
                displayKey="DescriptiveName"
                idKey="AccountId"
                required
              />
            </div>
            <div className="mb-4">
              <InputGroup
                label="Budget Name"
                name="budgetName"
                type="text"
                value={campaignFormData.budgetName}
                handleChange={handleCampaignFormChange}
                placeholder="Enter budget name"
                required
              />
            </div>

            <div className="mb-4">
              <InputGroup
                label="Budget Amount (Micros)"
                name="budgetAmountMicros"
                value={campaignFormData.budgetAmountMicros}
                handleChange={handleCampaignFormChange}
                placeholder="Enter budget amount in micros"
                required
              />
            </div>

            <div className="mb-4">
              <InputGroup
                label="Campaign Name"
                name="campaignName"
                type="text"
                value={campaignFormData.campaignName}
                handleChange={handleCampaignFormChange}
                placeholder="Enter campaign name"
                required
              />
            </div>
          </form>
        </DialogContent>

        <DialogActions sx={{ py: 2, px: 2 }}>
          <>
            <Button
              type="submit"
              label="Create Campaign"
              variant="primary"
              shape="rounded"
              disabled={campaignModalLoading}
              onClick={handleAddCampaign}
            />
          </>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ArticlePage;