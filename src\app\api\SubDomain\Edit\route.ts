import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { writeFile } from 'fs/promises';
import path from 'path';
import { randomUUID } from 'crypto';

// const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

// === Helpers ===

function getString(formData: FormData, key: string, fallback?: string | null): string | undefined {
    const val = formData.get(key);
    const fallbackSanitized = fallback ?? undefined;
    return typeof val === 'string' && val.trim() !== '' ? val : fallbackSanitized;
}

function getArray(formData: FormData, key: string, fallback: string[] = []): string[] {
    const val = getString(formData, key);
    try {
        return val ? JSON.parse(val) : fallback;
    } catch {
        return fallback;
    }
}

// async function saveFile(file: File | null): Promise<string | null> {
//     if (!file) return null;
//     const buffer = Buffer.from(await file.arrayBuffer());
//     const ext = path.extname(file.name);
//     const filename = `${randomUUID()}${ext}`;
//     const filepath = path.join(UPLOAD_DIR, filename);
//     await writeFile(filepath, buffer);
//     return filename;
// }

// === Main PUT Handler ===

export async function PUT(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const formData = await req.formData();
        const Id = getString(formData, 'Id');
        if (!Id) return NextResponse.json({ error: 'Id is required' }, { status: 400 });

        const existingSubdomain = await prisma.subDomain.findUnique({ where: { Id, IsDeleted: false } });
        if (!existingSubdomain) {
            return NextResponse.json({ error: 'Subdomain not found' }, { status: 404 });
        }

        // Required Fields
        const Name = getString(formData, 'Name', existingSubdomain.Name);
        const Url = getString(formData, 'Url', existingSubdomain.Url);
        const Domain = getString(formData, 'Domain', existingSubdomain.Domain);
        const AssignUsers = getArray(formData, 'AssignUsers');


        // Optional Fields
        const AccountId = getArray(formData, 'AccountId', existingSubdomain.AccountId ?? []);
        const CId = getString(formData, 'CId', existingSubdomain.CId ?? undefined);
        const HeadTag = getString(formData, 'HeadTag', existingSubdomain.HeadTag ?? undefined);
        const HeadTagScript = getString(formData, 'HeadTagScript', existingSubdomain.HeadTagScript ?? undefined);
        const HeadTagScriptLandingPage = getString(formData, 'HeadTagScriptLandingPage', existingSubdomain.HeadTagScriptLandingPage ?? undefined);
        const HeadTagScriptSearchPage = getString(formData, 'HeadTagScriptSearchPage', existingSubdomain.HeadTagScriptSearchPage ?? undefined);
        const GId = getString(formData, 'GId', existingSubdomain.GId ?? undefined);
        const AWId = getString(formData, 'AWId', existingSubdomain.AWId ?? undefined);
        const SendTo = getString(formData, 'SendTo', existingSubdomain.SendTo ?? undefined);

        // // File Uploads
        // const WideLogo = formData.get('WideLogo') as File | null;
        // const SquareLogo = formData.get('SquareLogo') as File | null;

        // const wideLogoPath = await saveFile(WideLogo);
        // const squareLogoPath = await saveFile(SquareLogo);

        if (Name !== existingSubdomain.Name) {
            const duplicate = await prisma.subDomain.findFirst({
                where: {
                    Name,
                    Domain,
                    IsDeleted: false,
                    Id: { not: Id }
                }
            });
            if (duplicate) {
                return NextResponse.json({ error: 'Subdomain with this name already exists in the domain' }, { status: 409 });
            }
        }

        if (Url !== existingSubdomain.Url) {
            const duplicateUrl = await prisma.subDomain.findFirst({
                where: {
                    Url,
                    Domain,
                    IsDeleted: false,
                    Id: { not: Id }
                }
            });
            if (duplicateUrl) {
                return NextResponse.json({ error: 'Subdomain with this URL already exists in the domain' }, { status: 409 });
            }
        }

        const updateData: any = {
            Name,
            Url,
            Domain,
            AccountId,
            CId,
            HeadTag,
            HeadTagScript,
            HeadTagScriptLandingPage,
            HeadTagScriptSearchPage,
            GId,
            AWId,
            SendTo,
            UpdatedAt: new Date()
        };

        // if (wideLogoPath) updateData.WideLogo = wideLogoPath;
        // if (squareLogoPath) updateData.SquareLogo = squareLogoPath;

        // Update and assign users in transaction
        const result = await prisma.$transaction(async (tx) => {
            const updatedSubdomain = await tx.subDomain.update({
                where: { Id },
                data: updateData
            });

            await tx.subDomainUserMappings.deleteMany({ where: { SubDomainId: Id } });

            const mappings = await Promise.all(
                AssignUsers.map(userId =>
                    tx.subDomainUserMappings.create({
                        data: {
                            SubDomainId: Id,
                            UserId: userId
                        }
                    })
                )
            );

            return { updatedSubdomain, mappings };
        });

        return NextResponse.json({
            success: true,
            message: 'Subdomain updated successfully',
            mappingsUpdated: result.mappings.length
        });

    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(
            { error: 'Internal Server Error', details: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}