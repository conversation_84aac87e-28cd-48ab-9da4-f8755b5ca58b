import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const orderBy = searchParams.get("orderBy") || "ClickCount";
        const orderDir = searchParams.get("orderDir") || "asc";
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");
        if (!startDate || !endDate) {
            throw new Error(" startDate and endDate are required.");
        }

        let whereCondition: any = {};

        if (startDate && endDate) {
            const startDateTime = new Date(startDate + 'T00:00:00.000Z');
            const endDateTime = new Date(endDate + 'T23:59:59.999Z');

            whereCondition.Created_At = {
                gte: startDateTime,
                lte: endDateTime
            };
        }

        // Add search functionality
        if (search && search !== "") {
            whereCondition.OR = [
                { URL: { contains: search, mode: 'insensitive' } },
                { Keyword: { contains: search, mode: 'insensitive' } },
                { IpAddress: { contains: search, mode: 'insensitive' } },
                { Domain: { contains: search, mode: 'insensitive' } }
            ];
        }

        // Get aggregated data using Prisma's groupBy
        const aggregatedData = await prisma.queryAnalytics.groupBy({
            by: ['URL', 'Domain'],
            _sum: {
                Count: true,
                AdsClickCounter: true
            },
            where: whereCondition,
            orderBy: orderBy === 'ClickCount' ? { _sum: { Count: orderDir as 'asc' | 'desc' } } :
                orderBy === 'AdsClickCount' ? { _sum: { AdsClickCounter: orderDir as 'asc' | 'desc' } } :
                    orderBy === 'URL' ? { URL: orderDir as 'asc' | 'desc' } :
                        { Domain: orderDir as 'asc' | 'desc' }
        });

        // Transform the data to match your expected format
        const transformedData = aggregatedData.map(item => ({
            ClickCount: item._sum.Count || 0,
            AdsClickCount: item._sum.AdsClickCounter || 0,
            URL: item.URL,
            Domain: item.Domain,
            StartDate: startDate,
            EndDate: endDate
        }));

        // Get total count for pagination
        const totalRecords = transformedData.length;

        // Apply pagination
        let paginatedData = transformedData;
        let skip: number | undefined;
        let limit: number | undefined;

        if (length !== -1) {
            const skipCount = (start === 1 ? 0 : start - 1) * length;
            skip = skipCount > 0 ? skipCount : 0;
            limit = length;
            paginatedData = transformedData.slice(skip, skip + limit);
        }

        const totalPages = length === -1 ? 1 : Math.ceil(totalRecords / length);

        return NextResponse.json({
            success: true,
            data: paginatedData

        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
