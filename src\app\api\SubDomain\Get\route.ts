import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        // DataTables-style parameters
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const domainId = searchParams.get("DomainId");
        const draw = parseInt(searchParams.get("draw") || "1");

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        if (!domainId) {
            return NextResponse.json(
                { error: "DomainId parameter is required" },
                { status: 400 }
            );
        }

        // Get sorting parameters
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        // Define allowed sortable fields (must match Prisma model exactly)
        const allowedOrderFields = ['Name', 'CId', 'CreatedAt', 'Url', 'HeadTag', 'Articles'];
        const sanitizedOrderBy = allowedOrderFields.includes(orderBy)
            ? orderBy
            : 'CreatedAt';

        // Create orderBy clause
        let orderByClause = {};
        if (sanitizedOrderBy === 'DomainName') {
            orderByClause = {
                Domain_SubDomain_DomainToDomain: {
                    Name: orderDir
                }
            };
        } else {
            orderByClause = {
                [sanitizedOrderBy]: orderDir
            };
        }

        let where: any = {
            Domain: domainId,
            IsDeleted: false
        };

        if (user.User_Type !== 'Super Admin' && user.User_Type !== 'Admin') {
            where.CreatedBy = user.Id;
        }
        if (search && search !== "") {
            where = {
                ...where,
                OR: [
                    { Name: { contains: search, mode: 'insensitive' } },
                    { Url: { contains: search, mode: 'insensitive' } },
                    { CId: { contains: search, mode: 'insensitive' } },
                ]
            };
        }

        // Get total count (unfiltered)
        const recordsTotal = await prisma.subDomain.count({
            where: {
                Domain: domainId,
                IsDeleted: false
            }
        });

        // Get filtered count
        const recordsFiltered = await prisma.subDomain.count({ where });

        // Get paginated data
        const SubDomainsWithUsers = await prisma.subDomain.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            include: {
                Domain_SubDomain_DomainToDomain: {
                    select: {
                        Name: true
                    }
                },
                SubDomainUserMappings: {
                    select: {
                        UserId: true
                    }
                },
                _count: {
                    select: {
                        SubDomainUserMappings: {
                            where: {
                                AdminUser: {
                                    IsDeleted: false
                                }
                            }
                        }
                    }
                }
            }
        });

        // Transform the data
        const transformedData = SubDomainsWithUsers.map(SubDomain => ({
            Id: SubDomain.Id,
            Name: SubDomain.Name,
            Url: SubDomain.Url,
            CId: SubDomain.CId,
            HeadTag: SubDomain.HeadTag,
            Articles: SubDomain.Articles,
            AccountId: SubDomain.AccountId,
            HeadTagScript: SubDomain.HeadTagScript,
            HeadTagScriptLandingPage: SubDomain.HeadTagScriptLandingPage,
            HeadTagScriptSearchPage: SubDomain.HeadTagScriptSearchPage,
            GId: SubDomain.GId,
            AWId: SubDomain.AWId,
            SendTo: SubDomain.SendTo,
            DomainName: SubDomain.Domain_SubDomain_DomainToDomain?.Name,
            CreatedAt: SubDomain.CreatedAt,
            UserMappings: SubDomain.SubDomainUserMappings.map(mapping => mapping.UserId),
            UserCount: SubDomain._count.SubDomainUserMappings
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                draw,
                //recordsTotal,
                recordsFiltered,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}