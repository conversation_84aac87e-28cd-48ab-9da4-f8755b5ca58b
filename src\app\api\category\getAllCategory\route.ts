import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);

        // Pagination and filtering
        const page = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const orderByParam = searchParams.get("orderBy") || "Name";
        const orderDirParam = searchParams.get("orderDir") || "desc";

        const skip = length === -1 ? undefined : Math.max(0, (page - 1) * length);
        const take = length === -1 ? undefined : length;

        // Filtering conditions
        let where: any = {
            IsDeleted: false
        };

        if (search && search !== "") {
            where.Name = {
                contains: search,
                mode: 'insensitive'
            };
        }

        // Validate and map orderBy
        const allowedOrderFields = ['Name', 'ShowUrlName'];
        const normalizedOrderBy = orderByParam.toLowerCase();
        const orderDir = orderDirParam.toLowerCase() === 'asc' ? 'asc' : 'desc';

        const orderByMapping: Record<string, string> = {
            'name': 'Name',
            'showurlname': 'ShowUrlName'
        };

        const orderByField = orderByMapping[normalizedOrderBy] || 'Name';

        if (!allowedOrderFields.includes(orderByField)) {
            return NextResponse.json(
                { error: `Invalid orderBy parameter. Allowed values: ${allowedOrderFields.join(', ')}` },
                { status: 400 }
            );
        }

        // Fetch data
        const categories = await prisma.category.findMany({
            where,
            select: {
                Id: true,
                Name: true,
                ShowUrlName: true
            },
            skip,
            take,
            orderBy: {
                [orderByField]: orderDir
            }
        });

        const recordsTotal = await prisma.category.count({
            where: {
                IsDeleted: false
            }
        });

        const recordsFiltered = await prisma.category.count({ where });

        const totalPages = length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1));

        return NextResponse.json({
            success: true,
            data: categories,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                currentPageCount: categories.length,
                start: page,
                length,
                currentPage: page,
                totalPages,
                hasNextPage: length === -1 ? false : page * length < recordsFiltered,
                hasPreviousPage: page > 1,
            }
        });

    } catch (error) {
        console.error('Error fetching categories:', error);
        return NextResponse.json(
            {
                error: 'Failed to fetch categories',
                details: process.env.NODE_ENV === 'development' ?
                    error instanceof Error ? error.message : String(error)
                    : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
