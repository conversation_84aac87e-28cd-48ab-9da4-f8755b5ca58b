import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            Id: string;
            User_Type: string;
        };
        const user = (await verifyToken(req)) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const Id = searchParams.get('id');


        const validOrderFields = ['CreatedAt', 'Title', 'UpdatedAt'];

        let where: any = {
            IsDeleted: false,
            Id: Id
        };



        // Fetch articles
        const articles = await prisma.articleDetails.findMany({
            where,
        });

        const articleIds = articles.map((article) => article.Id);

        const campaignMappings = await prisma.articleCampaignMappings.findMany({
            where: {
                ArticleId: { in: articleIds },
            },
            select: {
                ArticleId: true,
                CampaignId: true,
            },
        });

        const campaignIds = [...new Set(campaignMappings.map((mapping) => mapping.CampaignId))].filter(
            (id): id is number => id !== null && id !== undefined
        );

        const campaigns =
            campaignIds.length > 0
                ? await prisma.ads_Campaigns.findMany({
                    where: { SNo: { in: campaignIds } },
                    select: { SNo: true, Name: true },
                })
                : [];

        const campaignMap = new Map(campaigns.map((campaign) => [campaign.SNo, campaign]));
        const articleCampaignMap = new Map<string, number[]>();

        campaignMappings.forEach((mapping) => {
            if (mapping.ArticleId && mapping.CampaignId) {
                if (!articleCampaignMap.has(mapping.ArticleId)) {
                    articleCampaignMap.set(mapping.ArticleId, []);
                }
                articleCampaignMap.get(mapping.ArticleId)!.push(mapping.CampaignId);
            }
        });

        const categoryIds = [...new Set(articles.map((article) => article.Category).filter(Boolean))];
        const channelIds = [...new Set(articles.map((article) => article.CustomChannal).filter(Boolean))];
        const domainIds = [...new Set(articles.map((article) => article.Domain).filter(Boolean))];
        const subDomainIds = [...new Set(articles.map((article) => article.SubDomain).filter(Boolean))];
        const adminUserIds = [...new Set(articles.map((article) => article.User_Id_Settings).filter(Boolean))];

        const filteredCategoryIds = categoryIds.filter((id): id is string => id !== null);
        const filteredChannelIds = channelIds.filter((id): id is string => id !== null);
        const filteredDomainIds = domainIds.filter((id): id is string => id !== null);
        const filteredSubDomainIds = subDomainIds.filter((id): id is string => id !== null);

        const categories =
            filteredCategoryIds.length > 0
                ? await prisma.category.findMany({
                    where: { Id: { in: filteredCategoryIds } },
                    select: { Id: true, Name: true },
                })
                : [];

        const channels =
            filteredChannelIds.length > 0
                ? await prisma.channals.findMany({
                    where: { Id: { in: filteredChannelIds } },
                    select: { Id: true, Name: true, DisplayName: true },
                })
                : [];

        const domains =
            filteredDomainIds.length > 0
                ? await prisma.domain.findMany({
                    where: { Id: { in: filteredDomainIds } },
                    select: { Id: true, Name: true },
                })
                : [];

        const subDomains =
            filteredSubDomainIds.length > 0
                ? await prisma.subDomain.findMany({
                    where: { Id: { in: filteredSubDomainIds } },
                    select: { Id: true, Name: true },
                })
                : [];

        const adminUsers =
            adminUserIds.length > 0
                ? await prisma.adminUser.findMany({
                    where: { Id: { in: adminUserIds as string[] } },
                    select: { Id: true, Name: true },
                })
                : [];

        const categoryMap = new Map(categories.map((cat) => [cat.Id, cat.Name]));
        const channelMap = new Map(channels.map((ch) => [ch.Id, { Name: ch.Name, DisplayName: ch.DisplayName }]));
        const domainMap = new Map(domains.map((domain) => [domain.Id, domain.Name]));
        const subDomainMap = new Map(subDomains.map((subDomain) => [subDomain.Id, subDomain.Name]));
        const adminUserMap = new Map(adminUsers.map((adminUser) => [adminUser.Id, adminUser.Name]));

        const extractCustomChannalId = (channelName: string | null | undefined): string | null => {
            if (!channelName) return null;
            const parts = channelName.split('/');
            return parts.length > 0 ? parts[parts.length - 1] : null;
        };

        let transformedArticles = articles.map((article) => {
            const channelData = article.CustomChannal ? channelMap.get(article.CustomChannal) : null;
            const channelName = channelData?.DisplayName || channelData?.Name || null;

            const articleCampaignIds = articleCampaignMap.get(article.Id) || [];
            const articleCampaigns = articleCampaignIds
                .map((campaignId) => {
                    const campaign = campaignMap.get(campaignId);
                    return campaign
                        ? {
                            SNo: campaign.SNo,
                            Name: campaign.Name,
                        }
                        : null;
                })
                .filter(Boolean);

            return {
                ...article,
                CategoryId: article.Category || null,
                CategoryName: article.Category ? categoryMap.get(article.Category) || null : null,

                ChannelId: article.CustomChannal || null,
                ChannelName: channelName,
                CustomChannalId: extractCustomChannalId(channelData?.Name),

                DomainId: article.Domain || null,
                DomainName: article.Domain ? domainMap.get(article.Domain) || null : null,

                SubDomainId: article.SubDomain || null,
                SubDomainName: article.SubDomain ? subDomainMap.get(article.SubDomain) || null : null,
                ReadTime: article.ReadTime || null,
                Campaigns: articleCampaigns,

                Category: undefined,
                CustomChannal: undefined,
                Domain: undefined,
                SubDomain: undefined,
                UserName: article.User_Id_Settings ? adminUserMap.get(article.User_Id_Settings) || null : null,
                
            };
        });


        return NextResponse.json({
            success: true,
            data: transformedArticles,

        });
    } catch (error) {
        console.error('Error fetching articles:', error);
        return NextResponse.json({ error: 'Article Not Found' }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}
