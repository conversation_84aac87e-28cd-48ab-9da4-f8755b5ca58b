import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";


type SubDomainUserMapping = {
    Id: string;
    CreatedAt: Date | null;
    UserId: string | null;
    V: number | null;
    SubDomainId: string | null;
};

function getString(formData: FormData, key: string): string | null {
    const val = formData.get(key);
    return typeof val === 'string' ? val.trim() : null;
}

function getNumber(formData: FormData, key: string): number | null {
    const val = getString(formData, key);
    return val ? Number(val) : null;
}

function getArray(formData: FormData, key: string): string[] {
    const val = getString(formData, key);
    return val ? JSON.parse(val) : [];
}

// async function saveFile(file: File | null, folder: string): Promise<string | null> {
//     if (!file) return null;
//     const buffer = Buffer.from(await file.arrayBuffer());
//     const ext = path.extname(file.name);
//     const fileName = `${folder}_${uuidv4()}${ext}`;
//     const filePath = path.join(UPLOAD_DIR, fileName);
//     await fs.writeFile(filePath, buffer);
//     return fileName;
// }

export async function POST(req: NextRequest) {
    try {
        const user = await verifyToken(req) as { Id: string };
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const formData = await req.formData();

        const Name = getString(formData, 'Name');
        const Url = getString(formData, 'Url');
        const Domain = getString(formData, 'Domain');
        const AssignUsers = getArray(formData, 'AssignUsers');

        if (!Name || !Url || !Domain) {
            return NextResponse.json(
                { error: 'Name, Url, and Domain are required' },
                { status: 400 }
            );
        }

        const AccountId = getArray(formData, 'AccountId');
        const CId = getString(formData, 'CId');
        const HeadTag = getString(formData, 'HeadTag');
        const HeadTagScript = getString(formData, 'HeadTagScript');
        const HeadTagScriptLandingPage = getString(formData, 'HeadTagScriptLandingPage');
        const HeadTagScriptSearchPage = getString(formData, 'HeadTagScriptSearchPage');
        const GId = getString(formData, 'GId');
        const AWId = getString(formData, 'AWId');
        const SendTo = getString(formData, 'SendTo');

        // const WideLogo = formData.get('WideLogo') as File | null;
        // const SquareLogo = formData.get('SquareLogo') as File | null;

        // const wideLogoPath = await saveFile(WideLogo, 'wide_logo');
        // const squareLogoPath = await saveFile(SquareLogo, 'square_logo');

        // Validate AssignUsers exist
        let validUsers: { Id: string }[] = [];

        if (AssignUsers.length > 0) {
            validUsers = await prisma.adminUser.findMany({
                where: {
                    Id: { in: AssignUsers },
                    IsDeleted: false
                },
                select: { Id: true }
            });

            if (validUsers.length !== AssignUsers.length) {
                const foundIds = validUsers.map(u => u.Id);
                const invalidIds = AssignUsers.filter(id => !foundIds.includes(id));
                return NextResponse.json({ error: 'Invalid user IDs', invalidIds }, { status: 400 });
            }
        }

        // Check if subdomain name already exists within the same domain
        const existingSubdomain = await prisma.subDomain.findFirst({
            where: {
                Name,
                Domain,
                IsDeleted: false
            }
        });

        if (existingSubdomain) {
            return NextResponse.json({
                error: `SubDomain '${Name}' already exists in domain '${Domain}'`
            }, { status: 409 });
        }

        const existingUrl = await prisma.subDomain.findFirst({
            where: {
                Url,
                Domain,
                IsDeleted: false
            }
        });

        if (existingUrl) {
            return NextResponse.json({
                error: `SubDomain with URL '${Url}' already exists in domain '${Domain}'`
            }, { status: 409 });
        }

        const result = await prisma.$transaction(async (tx) => {
            const newSubdomain = await tx.subDomain.create({
                data: {
                    Name,
                    Url,
                    Domain,
                    CId,
                    AccountId: AccountId.length ? AccountId : undefined,
                    HeadTag,
                    HeadTagScript,
                    HeadTagScriptLandingPage,
                    HeadTagScriptSearchPage,
                    GId,
                    AWId,
                    SendTo,
                    CreatedBy: user.Id,
                    CreatedAt: new Date(),
                    // WideLogo: wideLogoPath,
                    // SquareLogo: squareLogoPath,
                },
            });

            let mappings: SubDomainUserMapping[] = [];

            if (AssignUsers.length > 0) {
                mappings = await Promise.all(
                    AssignUsers.map((userId) =>
                        tx.subDomainUserMappings.create({
                            data: {
                                SubDomainId: newSubdomain.Id,
                                UserId: userId,
                            },
                        })
                    )
                );
            }
            return { subdomain: newSubdomain, mappings };
        });

        return NextResponse.json({
            success: true,
            message: 'SubDomain created successfully',
            subdomain: result.subdomain,
            mappingsCreated: result.mappings.length
        }, { status: 201 });

    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(
            { error: 'Internal Server Error', details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}