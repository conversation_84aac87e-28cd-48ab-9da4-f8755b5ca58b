"use client";
import React, { useCallback, useEffect, useState } from "react";
import axios from "axios";
import {
  formatDate,
  getCurrency,
  getLast7Days,
  decodeJWT,
} from "@/utils/functions";
import { Button } from "@/components/ui-elements/button";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import DateRangePicker from "@/components/DateRangePicker";
import { CircularProgress } from "@mui/material";
import Swal from "sweetalert2";

function Revenue() {
  const [Token, setToken] = useState(null);
  const [options, setOptions] = useState([]);
  const [customChannels, setCustomChannels] = useState([]);
  const [reportsTableData, setReportsTableData] = useState([]);
  const [toggle, setToggle] = useState(true);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [selectedPartner, setSelectedPartner] = useState("");
  const [showLoader, setShowLoader] = useState(false);
  const userData = Token ? decodeJWT(Token) : null;
  const [selectedBreakPoints, setSelectedBreakPoints] = useState(["customChannel"]);
  const [customChannelByUsers, setCustomChannelByUsers] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [tableColumns, setTableColumns] = useState([]);
  const [tablePage, setTablePage] = useState(0);
  const [tableRowsPerPage, setTableRowsPerPage] = useState(25);
  const [tableOrder, setTableOrder] = useState("asc");
  const [tableOrderBy, setTableOrderBy] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const getLast7DaysUntilYesterday = () => {
    const end = new Date();
    end.setDate(end.getDate() - 1); // yesterday
  
    const start = new Date(end); // start based on end date
    start.setDate(end.getDate() - 6); // 6 days before end
  
    return [start, end];
  };
  
  const [dateRange, setDateRange] = useState(getLast7DaysUntilYesterday());
  const [partnerUsers, setPartnerUsers] = useState([]);
  const [startDate, endDate] = dateRange;
  const [styleIds, setStyleIds] = useState([]);
  const [selectedStyleIds, setSelectedStyleIds] = useState([]);
  const [totalRows, setTotalRows] = useState({ cells: [] });
  const [recordsTotal, setRecordsTotal] = useState(0); // New state for total records
  const [recordsFiltered, setRecordsFiltered] = useState(0); // New state for filtered records

  
  const breakPointOptions = [
    { value: "country", label: "Country" },
    { value: "platform", label: "Platform" },
    { value: "date", label: "Date" },
    { value: "customChannel", label: "Custom Channel" },
    { value: "styleId", label: "Style IDs" },
  ];

  const fetchData = useCallback(async () => {
    try {
      const response = await axios.get(`/api/Channals/GetDropdown`, {
        withCredentials: true,
      });
      if (response?.status === 200) {
        setCustomChannels(response?.data?.data);
        setOptions(
          response?.data?.data?.map((channels) => ({
            label: channels?.DisplayName,
            value: channels?.Id,
          })),
        );
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);

  const fetchDataUsers = useCallback(async () => {
    try {
      const response = await axios.get(`/api/adminuser/GetDropdown`, {
        withCredentials: true,
      });
      if (response?.status === 200) {
        setPartnerUsers(response?.data?.data);
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);

  const fetchStyleIds = useCallback(async (userId = null) => {
    try {
      const url = userId
        ? `/api/StyleIds/getdropdown?userId=${userId}`
        : `/api/StyleIds/getdropdown`;
      const response = await axios.get(url, {
        withCredentials: true,
      });
      if (response?.status === 200) {
        setStyleIds(response?.data?.data || []);
        setSelectedStyleIds([]);
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
      setStyleIds([]);
      setSelectedStyleIds([]);
    }
  }, []);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken");
    setToken(accessToken);
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (["Admin", "Super Admin"]?.includes(userData?.User_Type)) {
      fetchDataUsers();
    }
  }, [fetchDataUsers, userData?.User_Type]);

  useEffect(() => {
    fetchStyleIds();
  }, [fetchStyleIds]);

  const fetchChannelByUser = () => {
    if (["Super Admin", "Admin"].includes(userData?.User_Type)) {
      axios
        .get(`/api/Channals/GetDropdown`, {
          withCredentials: true,
        })
        .then((response) => {
          if (response?.status === 200 && response?.data?.data) {
            setCustomChannels(response?.data?.data || []);
          }
        })
        .catch((error) => {
          console.error(
            "Error fetching all channels:",
            error?.response?.data?.error || "Internal server error",
          );
        });
      return;
    }

    axios
      .get(`/api/Channals/GetDropdown`, {
        withCredentials: true,
      })
      .then((response) => {
        if (response?.data?.data && response?.data?.data.length > 0) {
          setCustomChannelByUsers(response?.data?.data);
          const channelOptions = response?.data?.data?.map((channels) => ({
            label: channels?.DisplayName,
            value: channels?.Id,
          }));
          setOptions(channelOptions);
        } else {
          setCustomChannelByUsers([]);
          setOptions([]);
        }
      })
      .catch((error) => {
        console.error(
          "Error fetching user channels:",
          error?.response?.data?.error || "Internal server error",
        );
      });
  };

  useEffect(() => {
    fetchChannelByUser();
  }, [fetchData, userData?.User_Type]);

  const fetchDataPartner = useCallback(async () => {
    const selectedPartnerData = selectedPartner
      ? partnerUsers?.find((user) => user?.Id === selectedPartner)
      : null;
    const effectiveUserType =
      selectedPartnerData?.User_Type || userData?.User_Type;

    try {
      if (["Super Admin", "Admin"].includes(userData?.User_Type)) {
        if (selectedPartner && selectedPartner !== "") {
          if (["Partner", "Account"].includes(effectiveUserType)) {
            const channelsResponse = await axios.get(
              `/api/Channals/GetDropdown?userId=${selectedPartner}`,
              {
                withCredentials: true,
              },
            );
            if (
              channelsResponse?.status === 200 &&
              channelsResponse?.data?.data &&
              channelsResponse?.data?.data.length > 0
            ) {
              const filteredChannels = channelsResponse?.data?.data;
              const channelOptions = filteredChannels.map((channel) => ({
                label: channel?.DisplayName,
                value: channel?.Id,
              }));
              setOptions(channelOptions);
            } else {
              setOptions([]);
            }
          } else {
            const allOptions = customChannels?.map((channels) => ({
              label: channels?.DisplayName,
              value: channels?.Id,
            }));
            setOptions(allOptions || []);
          }
        } else {
          const allOptions = customChannels?.map((channels) => ({
            label: channels?.DisplayName,
            value: channels?.Id,
          }));
          setOptions(allOptions);
        }
      } else if (["Partner", "Account"].includes(userData?.User_Type)) {
        const filteredOptions = customChannelByUsers?.map((channels) => ({
          label: channels?.DisplayName,
          value: channels?.Id,
        }));
        setOptions(filteredOptions);
      }
    } catch (error) {
      console.error(
        "Error in fetchDataPartner:",
        error?.response?.data?.error || "Internal server error",
      );
    }
  }, [
    customChannels,
    selectedPartner,
    customChannelByUsers,
    userData?.User_Type,
    partnerUsers,
    Token,
  ]);

  useEffect(() => {
    fetchDataPartner();
  }, [fetchDataPartner]);

  const handleBreakPoints = (selectedValues) => {
    setSelectedBreakPoints(selectedValues);
    setTablePage(0); // Reset page on breakpoint change
  };

  // const searchApi = async () => {
  //   const selectedChannelOptions =
  //     selectedOptions["userIdCustomReport"]?.map((dataReport) => dataReport?.value) || [];
  //   let channelsToUse = selectedChannelOptions;

  //   const breakPointsObj = {
  //     country: selectedBreakPoints.includes("country"),
  //     platform: selectedBreakPoints.includes("platform"),
  //     date: selectedBreakPoints.includes("date"),
  //     customChannel: selectedBreakPoints.includes("customChannel"),
  //     styleId: selectedBreakPoints.includes("styleId"),
  //   };

  //   const column = tableColumns.find((col) => col.id === tableOrderBy);
  //   const backendOrderBy = column ? column.field : "date";

  //   const obj = {
  //     breakPoints: JSON.stringify(breakPointsObj),
  //     selectedOptions: JSON.stringify(channelsToUse),
  //     selectedPartner,
  //     toggle: toggle.toString(),
  //     orderBy: backendOrderBy,
  //     orderDir: tableOrder,
  //     start: (tablePage * tableRowsPerPage).toString(),
  //     length: tableRowsPerPage.toString(),
  //     search: encodeURIComponent(searchTerm),
  //     draw: "1",
  //   };

  //   const params = new URLSearchParams(obj).toString();

  //   try {
  //     setShowLoader(true);
  //     const response = await axios.post(
  //       `/api/Revenue/Get?startDate=${formatDate(startDate)}&endDate=${formatDate(endDate)}&${params}`,
  //       {
  //         selectedChannels: channelsToUse,
  //         selectedStyles: selectedStyleIds,
  //       },
  //       {
  //         withCredentials: true,
  //       }
  //     );

  //     if (response?.status === 200 && response.data.success) {
  //       setShowLoader(false);
  //       const data = response.data.data || [];
  //       const pagination = response.data.pagination || {
  //         recordsTotal: 0,
  //         recordsFiltered: 0,
  //       };

  //       setRecordsTotal(pagination.recordsTotal || 0);
  //       setRecordsFiltered(pagination.recordsFiltered || 0);

  //       if (data.length === 0) {
  //         setReportsTableData([]);
  //         setTotalRows({ cells: [] });
  //         return;
  //       }

  //       const dimensionColumns = [];
  //       if (breakPointsObj.customChannel) {
  //         dimensionColumns.push({ name: "CHANNEL", type: "DIMENSION" });
  //       }
  //       if (breakPointsObj.date) {
  //         dimensionColumns.push({ name: "DATE", type: "DIMENSION" });
  //       }
  //       if (breakPointsObj.country) {
  //         dimensionColumns.push({ name: "COUNTRY", type: "DIMENSION" });
  //       }
  //       if (breakPointsObj.platform) {
  //         dimensionColumns.push({ name: "PLATFORM", type: "DIMENSION" });
  //       }
  //       if (breakPointsObj.styleId) {
  //         dimensionColumns.push({ name: "STYLEID", type: "DIMENSION" });
  //       }

  //       const metricColumns = [
  //         { name: "ESTIMATEDEARNINGS", type: "METRIC", currencyCode: toggle ? "INR" : "USD" },
  //         { name: "IMPRESSIONS", type: "METRIC" },
  //         { name: "IMPRESSIONSRPM", type: "METRIC" },
  //         { name: "CLICKS", type: "METRIC" },
  //         { name: "IMPRESSIONSCTR", type: "METRIC" },
  //         { name: "COSTPERCLICK", type: "METRIC" },
  //       ];

  //       const headers = [...dimensionColumns, ...metricColumns];
  //       const fieldMap = {
  //         CHANNEL: "customChannel",
  //         DATE: "date",
  //         COUNTRY: "country",
  //         PLATFORM: "platformType",
  //         STYLEID: "customSearchStyle",
  //         ESTIMATEDEARNINGS: "estimatedEarnings",
  //         IMPRESSIONS: "impressions",
  //         IMPRESSIONSRPM: "impressionsRpm",
  //         CLICKS: "clicks",
  //         IMPRESSIONSCTR: "impressionsCtr",
  //         COSTPERCLICK: "costPerClick",
  //       };

  //       const rows = data.map((item, index) => {
  //         const rowCells = headers.map((header) => {
  //           let value;
  //           const fieldName = fieldMap[header.name];
  //           if (header.name === "CHANNEL") {
  //             value = item[fieldName] != null ? item[fieldName] : "N/A";
  //           } else if (header.type === "METRIC" && header.name === "ESTIMATEDEARNINGS") {
  //             const rawValue = item[fieldName];
  //             value = rawValue != null ? rawValue.toString() : "0";
  //           } else {
  //             value = item[fieldName] != null ? item[fieldName].toString() : "";
  //           }
  //           return { value };
  //         });
  //         return { id: index, cells: rowCells };
  //       });

  //       const totals = {
  //         cells: headers.map((header) => {
  //           if (header.type === "METRIC") {
  //             const fieldName = fieldMap[header.name];
  //             const sum = data.reduce(
  //               (acc, item) => acc + (parseFloat(item[fieldName]) || 0),
  //               0
  //             );
  //             const value = header.name === "ESTIMATEDEARNINGS" ? sum.toFixed(2) : sum.toString();
  //             return { value };
  //           }
  //           return { value: "" };
  //         }),
  //       };

  //       setReportsTableData([{ headers, rows, totals, originalData: data }]);
  //       setTotalRows(totals);
  //     } else {
  //       setShowLoader(false);
  //       setReportsTableData([]);
  //       setTotalRows({ cells: [] });
  //       setRecordsTotal(0);
  //       setRecordsFiltered(0);
  //     }
  //   } catch (error) {
  //     setShowLoader(false);
  //     Swal.fire({
  //       title: "No Data Found",
  //       text: error.message || error?.response?.data?.error,
  //       icon: "error",
  //       confirmButtonColor: "#5750f1",
  //     });
  //     setRecordsTotal(0);
  //     setRecordsFiltered(0);
  //   }
  // };
  const searchApi = async () => {
    const selectedChannelOptions =
      selectedOptions["userIdCustomReport"]?.map((dataReport) => dataReport?.value) || [];
    let channelsToUse = selectedChannelOptions;

    const breakPointsObj = {
      country: selectedBreakPoints.includes("country"),
      platform: selectedBreakPoints.includes("platform"),
      date: selectedBreakPoints.includes("date"),
      customChannel: selectedBreakPoints.includes("customChannel"),
      styleId: selectedBreakPoints.includes("styleId"),
    };

    const column = tableColumns.find((col) => col.id === tableOrderBy);
    const backendOrderBy = column ? column.field : "date";

    const obj = {
      breakPoints: JSON.stringify(breakPointsObj),
      selectedOptions: JSON.stringify(channelsToUse),
      userId : selectedPartner,
      toggle: toggle.toString(),
      orderBy: backendOrderBy,
      orderDir: tableOrder,
      start: (tablePage * tableRowsPerPage).toString(),
      length: tableRowsPerPage.toString(),
      search: encodeURIComponent(searchTerm),
      draw: "1",
    };

    const params = new URLSearchParams(obj).toString();

    try {
      setShowLoader(true);
      const response = await axios.post(
        `/api/Revenue/Get?startDate=${formatDate(startDate)}&endDate=${formatDate(endDate)}&${params}`,
        {
          selectedChannels: channelsToUse,
          selectedStyles: selectedStyleIds,
        },
        {
          withCredentials: true,
        }
      );

      if (response?.status === 200 && response.data.success) {
        setShowLoader(false);
        const data = response.data.data || [];
        const pagination = response.data.pagination || {
          recordsTotal: 0,
          recordsFiltered: 0,
        };

        setRecordsTotal(pagination.recordsTotal || 0);
        setRecordsFiltered(pagination.recordsFiltered || 0);

        if (data.length === 0) {
          setReportsTableData([]);
          setTotalRows({ cells: [] });
          return;
        }

        const dimensionColumns = [];
        if (breakPointsObj.customChannel) {
          dimensionColumns.push({ name: "CHANNEL", type: "DIMENSION" });
        }
        if (breakPointsObj.date) {
          dimensionColumns.push({ name: "DATE", type: "DIMENSION" });
        }
        if (breakPointsObj.country) {
          dimensionColumns.push({ name: "COUNTRY", type: "DIMENSION" });
        }
        if (breakPointsObj.platform) {
          dimensionColumns.push({ name: "PLATFORM", type: "DIMENSION" });
        }
        if (breakPointsObj.styleId) {
          dimensionColumns.push({ name: "STYLEID", type: "DIMENSION" });
        }

        const metricColumns = [
          { name: "ESTIMATEDEARNINGS", type: "METRIC", currencyCode: toggle ? "INR" : "USD" },
          { name: "IMPRESSIONS", type: "METRIC" },
          { name: "IMPRESSIONSRPM", type: "METRIC" },
          { name: "CLICKS", type: "METRIC" },
          { name: "IMPRESSIONSCTR", type: "METRIC" },
          { name: "COSTPERCLICK", type: "METRIC" },
        ];

        const headers = [...dimensionColumns, ...metricColumns];
        const fieldMap = {
          CHANNEL: "customChannel",
          DATE: "date",
          COUNTRY: "country",
          PLATFORM: "platformType",
          STYLEID: "customSearchStyle",
          ESTIMATEDEARNINGS: "estimatedEarnings",
          IMPRESSIONS: "impressions",
          IMPRESSIONSRPM: "impressionsRpm",
          CLICKS: "clicks",
          IMPRESSIONSCTR: "impressionsCtr",
          COSTPERCLICK: "costPerClick",
        };

        // Create rows exactly as they come from API
        const rows = data.map((item, index) => {
          const rowCells = headers.map((header) => {
            let value;
            const fieldName = fieldMap[header.name];
            
            if (header.name === "CHANNEL") {
              value = item[fieldName] != null ? item[fieldName] : "N/A";
            } else if (header.type === "METRIC" && header.name === "ESTIMATEDEARNINGS") {
              const rawValue = item[fieldName];
              value = rawValue != null ? rawValue.toString() : "0";
            } else {
              value = item[fieldName] != null ? item[fieldName].toString() : "";
            }
            return { value };
          });
          return { id: index, cells: rowCells };
        });

        // Calculate sums for totals row
        const sums = data.reduce(
          (acc, item) => {
            return {
              estimatedEarnings: acc.estimatedEarnings + (parseFloat(item.estimatedEarnings) || 0),
              impressions: acc.impressions + (parseFloat(item.impressions) || 0),
              clicks: acc.clicks + (parseFloat(item.clicks) || 0),
            };
          },
          { estimatedEarnings: 0, impressions: 0, clicks: 0 }
        );

        // Apply special formulas ONLY to totals row
        const totals = {
          cells: headers.map((header) => {
            if (header.type === "METRIC") {
              switch (header.name) {
                case "ESTIMATEDEARNINGS":
                  return {
                    value: `${getCurrency(header.currencyCode)}${sums.estimatedEarnings.toFixed(2)}`
                  };
                
                case "IMPRESSIONS":
                  return {
                    value: sums.impressions.toLocaleString()
                  };
                
                case "IMPRESSIONSRPM":
                  const rpm = sums.impressions > 0 
                    ? (sums.estimatedEarnings / sums.impressions) * 1000 
                    : 0;
                  return {
                    value: rpm.toFixed(2)
                  };
                
                case "CLICKS":
                  return {
                    value: sums.clicks.toLocaleString()
                  };
                
                case "IMPRESSIONSCTR":
                  const ctr = sums.impressions > 0 
                    ? (sums.clicks / sums.impressions) * 100 
                    : 0;
                  return {
                    value: `${ctr.toFixed(2)}%`
                  };
                
                case "COSTPERCLICK":
                  const rpc = sums.clicks > 0 
                    ? sums.estimatedEarnings / sums.clicks 
                    : 0;
                  return {
                    value: `${getCurrency(header.currencyCode)}${rpc.toFixed(2)}`
                  };
                
                default:
                  return { value: "" };
              }
            }
            return { value: "" };
          }),
        };

        setReportsTableData([{ headers, rows, totals, originalData: data }]);
        setTotalRows(totals);
      } else {
        setShowLoader(false);
        setReportsTableData([]);
        setTotalRows({ cells: [] });
        setRecordsTotal(0);
        setRecordsFiltered(0);
      }
    } catch (error) {
      setShowLoader(false);
      Swal.fire({
        title: "No Data Found",
        text: error.message || error?.response?.data?.error,
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
      setRecordsTotal(0);
      setRecordsFiltered(0);
    }
  };
  const transformDataForTable = useCallback(() => {
    if (!reportsTableData?.length) {
      setTableColumns([]);
      setTableData([]);
      setTotalRows({ cells: [] });
      return;
    }

    const headers = reportsTableData[0]?.headers || [];

    const columnNameMap = {
      CHANNEL: "Custom Channel",
      ESTIMATEDEARNINGS: "Estimated Earnings",
      IMPRESSIONS: "Impressions",
      IMPRESSIONSRPM: "Impressions RPM",
      CLICKS: "Clicks",
      IMPRESSIONSCTR: "Impressions CTR",
      COSTPERCLICK: "Cost Per Click",
      DATE: "Date",
      COUNTRY: "Country",
      PLATFORM: "Platform",
      STYLEID: "Style ID",
    };

    const columns = headers.map((header, index) => ({
      id: `col_${index}`,
      label: columnNameMap[header.name] || header.name.toLowerCase().replaceAll("_", " "),
      field: header.name.toLowerCase(), // Map to backend field
    }));

    setTableColumns(columns);

    const combinedData = reportsTableData[0]?.rows || [];
    const transformedRows = combinedData.map((row, index) => {
      const rowData = { Id: row.id };
      row.cells?.forEach((cell, cellIndex) => {
        let value = cell?.value || "";
        const header = headers[cellIndex];
        const isCurrencyColumn = header?.currencyCode && header.name === "ESTIMATEDEARNINGS";
        const isMetricRatio = header?.name === "IMPRESSIONSCTR";

        if (header.name === "CHANNEL") {
          const originalData = reportsTableData[0]?.originalData[index];
          const channelName = originalData?.customChannel || value;
          value = channelName != null ? channelName : "N/A";
        }

        if (header.name === "STYLEID") {
          const originalData = reportsTableData[0]?.originalData[index];
          const styleName = originalData?.customSearchStyle || value;
          value = styleName != null ? styleName : "N/A";
        }

        if (header.name === "PLATFORM") {
          value = value || "Unknown";
          value = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
        }

        if (isCurrencyColumn) {
          const numericValue = parseFloat(value);
          value = isNaN(numericValue)
            ? `${getCurrency(header.currencyCode)}0.00`
            : `${getCurrency(header.currencyCode)}${numericValue.toFixed(2)}`;
        }

        if (isMetricRatio) {
          const numericValue = parseFloat(value);
          value = isNaN(numericValue) || numericValue === 0 ? "0%" : (numericValue).toFixed(2) + "%";
        }

        if (
          header?.type === "METRIC" &&
          !isMetricRatio &&
          header?.name !== "ESTIMATEDEARNINGS"
        ) {
          const numericValue = parseFloat(value);
          value = isNaN(numericValue) ? "0" : numericValue.toLocaleString();
        }

        rowData[`col_${cellIndex}`] = value;
      });

      return rowData;
    });

    setTableData(transformedRows);
    setTotalRows(reportsTableData[0]?.totals || { cells: [] });
  }, [reportsTableData]);

  const handleTablePageChange = (newPage) => {
    setTablePage(newPage);
    searchApi(); // Fetch data for the new page
  };

  const handleTableRowsPerPageChange = (newRowsPerPage) => {
    setTableRowsPerPage(newRowsPerPage);
    setTablePage(0);
    searchApi(); // Fetch data with new rows per page
  };

  const handleTableRequestSort = (_, property) => {
    const isAsc = tableOrderBy === property && tableOrder === "asc";
    setTableOrder(isAsc ? "desc" : "asc");
    setTableOrderBy(property);
    setTablePage(0); // Reset to first page on sort
    searchApi(); // Fetch sorted data
  };

  const handleTableSearchChange = (term) => {
    setSearchTerm(term);
    setTablePage(0); // Reset to first page on search
    searchApi(); // Fetch filtered data
  };

  function handleQuickDateSelect(range) {
    const today = new Date();
    let startDate, endDate;

    switch (range) {
      case "today":
        startDate = new Date(today);
        endDate = new Date(today);
        break;
      case "yesterday":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 1);
        endDate = new Date(startDate);
        break;
      case "thismonth":
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate =
          today?.getDate() >
          new Date(today.getFullYear(), today.getMonth() + 1, 0)?.getDate()
            ? new Date(today.getFullYear(), today.getMonth() + 1, 0)
            : today;
        break;
      case "lastmonth":
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        endDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case "last30days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 29);
        endDate = new Date(today);
        break;
      case "last7days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 6);
        endDate = new Date(today);
        break;
      default:
        startDate = null;
        endDate = null;
        break;
    }
    if (startDate && endDate) {
      setDateRange([startDate, endDate]);
      setTablePage(0); // Reset to first page on date change
    }
  }

  useEffect(() => {
    transformDataForTable();
  }, [transformDataForTable]);

  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white">
              Revenue Management
            </h1>
          </div>
        </div>
        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
          <div className="mb-6">
            <div className="grid grid-cols-1 items-center gap-4 sm:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-6">
              <div className="z-50 w-full min-w-34">
                <MultiSelectDropdown
                  label="Breakpoints"
                  placeholder="Select Breakpoints"
                  options={breakPointOptions}
                  value={selectedBreakPoints}
                  onChange={handleBreakPoints}
                  displayKey="label"
                  idKey="value"
                />
              </div>

              {["Admin", "Super Admin"]?.includes(userData?.User_Type) && (
                <div className="z-40 w-full min-w-34">
                  <SearchableDropdown
                    label="All Users"
                    placeholder="Select Users..."
                    options={[{ Id: "", Name: "All Users" }, ...partnerUsers]}
                    value={selectedPartner}
                    onChange={(partner) => {
                      setSelectedPartner(partner?.Id || "");
                      fetchStyleIds(partner?.Id || null);
                      setTablePage(0); // Reset page
                    }}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div>
              )}

              <div className="z-30 w-full min-w-34">
                <MultiSelectDropdown
                  label="Style IDs"
                  placeholder="Select Style IDs"
                  options={
                    styleIds?.map((style) => ({
                      value: style?.Id,
                      label: style?.Name,
                    })) || []
                  }
                  value={selectedStyleIds}
                  onChange={(selectedValues) => {
                    setSelectedStyleIds(selectedValues);
                    setTablePage(0); // Reset page
                  }}
                  displayKey="label"
                  idKey="value"
                />
              </div>

              <div className="z-20 w-full min-w-34">
                <MultiSelectDropdown
                  label="Custom Channels"
                  placeholder="Select Channels"
                  options={options}
                  value={
                    selectedOptions["userIdCustomReport"]?.map(
                      (item) => item.value,
                    ) || []
                  }
                  onChange={(selectedValues) => {
                    const selectedItems = options.filter((option) =>
                      selectedValues.includes(option.value),
                    );
                    setSelectedOptions((prev) => ({
                      ...prev,
                      userIdCustomReport: selectedItems,
                    }));
                    setTablePage(0); // Reset page
                  }}
                  displayKey="label"
                  idKey="value"
                />
              </div>

              <div className="w-full min-w-34">
                <DateRangePicker
                  startDate={startDate}
                  endDate={endDate}
                  onDateChange={(selectedDates) => {
                    setDateRange(selectedDates);
                    setTablePage(0); // Reset page
                  }}
                  onQuickDateSelect={handleQuickDateSelect}
                  label="Date Range"
                  placeholder="Select date range"
                  showQuickSelectButtons={true}
                />
              </div>

              <div className="flex mt-4 min-w-34">
                <Button
                  label="Search"
                  variant="primary"
                  shape="rounded"
                  className="w-full"
                  onClick={searchApi}
                  disabled={showLoader}
                />
              </div>
            </div>
          </div>

          {showLoader && reportsTableData?.length === 0 && (
            <div className="mt-6 flex items-center justify-center">
              <CircularProgress size={40} />
            </div>
          )}

          {reportsTableData?.length > 0 && (
            <div className="mt-6">
              <CustomDataTable
                isLoading={showLoader}
                columns={tableColumns}
                rows={tableData}
                searchTerm={searchTerm}
                onSearchChange={handleTableSearchChange}
                page={tablePage}
                rowsPerPage={tableRowsPerPage}
                onPageChange={handleTablePageChange}
                onRowsPerPageChange={handleTableRowsPerPageChange}
                totalCount={recordsFiltered} // Use recordsFiltered for total count
                order={tableOrder}
                orderBy={tableOrderBy}
                onRequestSort={handleTableRequestSort}
                notShowAction={true} // Disable action column
                totals={totalRows}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default Revenue;