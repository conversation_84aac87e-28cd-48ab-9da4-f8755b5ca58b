import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

export async function POST(req: NextRequest) {
    type AuthenticatedUser = {
        Id: string;
    };

    const user = await verifyToken(req) as AuthenticatedUser;
    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await req.formData();
    const file = formData.get('Image') as File | null;

    // Check if file is provided
    if (!file) {
        return NextResponse.json({
            error: 'No file provided'
        }, { status: 400 });
    }

    let imagePath: string | null = null;
    let uniqueId: string | null = null;

    try {
        const buffer = Buffer.from(await file.arrayBuffer());
        const mimeType = file.type;

        // Validate file type
        if (!mimeType.startsWith('image/')) {
            return NextResponse.json({
                error: 'Invalid file type. Only images are allowed.'
            }, { status: 400 });
        }

        const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
        uniqueId = uuidv4();

        if (!fs.existsSync(UPLOAD_DIR)) {
            fs.mkdirSync(UPLOAD_DIR, { recursive: true });
        }

        // Process image versions in parallel
        const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
            sharp(buffer).toBuffer(),
            sharp(buffer)
                .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                .toFormat(originalExtension, { quality: 80 })
                .toBuffer(),
            sharp(buffer)
                .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                .toFormat(originalExtension, { quality: 80 })
                .toBuffer()
        ]);

        // Save all versions
        const baseFileName = `${uniqueId}.${originalExtension}`;
        const versions = [
            { suffix: '', buffer: originalBuffer },
            { suffix: '_small', buffer: smallBuffer },
            { suffix: '_medium', buffer: mediumBuffer }
        ];

        await Promise.all(versions.map(({ suffix, buffer }) => {
            const fileName = `${uniqueId}${suffix}.${originalExtension}`;
            const filePath = path.resolve(UPLOAD_DIR, fileName);
            return fs.promises.writeFile(filePath, buffer);
        }));

        imagePath = `/uploads/${baseFileName}`;

        // Return success response
        return NextResponse.json({
            success: true,
            message: 'Image uploaded successfully',
            imagePath: imagePath,
        }, { status: 201 });

    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
        console.error("Image processing error:", errorMessage);

        // Clean up any partially created files
        if (uniqueId) {
            try {
                const filesToDelete = [
                    `${uniqueId}.jpg`,
                    `${uniqueId}_small.jpg`,
                    `${uniqueId}_medium.jpg`
                ];

                await Promise.all(filesToDelete.map(fileName => {
                    const filePath = path.resolve(UPLOAD_DIR, fileName);
                    return fs.promises.unlink(filePath).catch(() => { }); // Ignore errors
                }));
            } catch (cleanupError) {
                console.error("File cleanup error:", cleanupError);
            }
        }

        return NextResponse.json(
            { error: "Failed to process image", details: errorMessage },
            { status: 500 }
        );
    }
}