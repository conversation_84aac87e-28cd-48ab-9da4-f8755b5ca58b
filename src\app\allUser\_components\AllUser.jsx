"use client";
import React, { useCallback, useEffect, useState } from "react";
import axios from "axios";
import Swal from "sweetalert2";
import useDebounce from "@/hooks/useDebounce";
import { FaPlus } from "react-icons/fa";
import { IoCloseOutline } from "react-icons/io5";
import {
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  IconButton,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import { Box, Modal } from "@mui/material";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import InputGroup from "@/components/FormElements/InputGroup";
import Checkbox from "@/components/FormElements/checkbox";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import { CloseIcon } from "@/assets/icons";

const styleForModal = {
  position: "absolute",
  top: "50%",
  left: "50%",
  border: "none",
  transform: "translate(-50%, -50%)",
  width: { xs: "90%", sm: "70%", md: "50%", lg: "30%" },
  bgcolor: "background.paper",
  boxShadow: 24,
  p: 4,
  borderRadius: "16px",
  maxHeight: { xs: "90vh" },
  overflowY: "auto",
  scrollbarWidth: "none",
};

const AddNewUserModal = ({ isOpen, onClose, onSubmit, isLoading }) => {
  const [formData, setFormData] = useState({
    Name: "",
    Email: "",
    Number: "",
    User_Type: "",
    Password: "",
    DisplayName: "",
    ProfilePic: null,
    AccessExpiration: "",
    CreationDate: new Date().toISOString().split("T")[0],
    Status: false,
    AboutMe: "",
  });

  const [errors, setErrors] = useState({}); // Added state for validation errors

  useEffect(() => {
    if (isOpen) {
      const today = new Date().toISOString().split("T")[0];
      const expiration = new Date();
      expiration.setFullYear(expiration.getFullYear() + 1);
      setFormData({
        Name: "",
        Email: "",
        Number: "",
        User_Type: "",
        Password: "",
        DisplayName: "",
        ProfilePic: null,
        AccessExpiration: expiration.toISOString().split("T")[0],
        CreationDate: today,
        Status: false,
        AboutMe: "",
      });
      setErrors({}); // Reset errors when modal opens
    }
  }, [isOpen]);

  const handleChange = (e) => {
    const { name, value, files, type, checked } = e.target;

    let newValue;
    if (type === "checkbox") {
      newValue = checked;
    } else if (type === "file") {
      newValue = files?.[0] || null;
    } else if (name === "Number") {
      if (value && !/^\d{0,10}$/.test(value)) {
        return;
      }
      newValue = value;
    } else {
      newValue = value;
    }

    setFormData((prevData) => ({
      ...prevData,
      [name]: newValue,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    const requiredFields = [
      { key: "Name", label: "User Name" },
      { key: "Email", label: "Email" },
      { key: "User_Type", label: "User Type" },
      { key: "Password", label: "Password" },
    ];

    requiredFields.forEach(({ key, label }) => {
      if (!formData[key].trim()) {
        newErrors[key] = `${label} is required`;
      }
    });

    // Optional: Add email format validation
    if (formData.Email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.Email)) {
      newErrors.Email = "Invalid email format";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return; // Prevent submission and keep modal open if validation fails
    }
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          Add New User
        </span>
        <IconButton
          aria-label="close"
          onClick={() => {
            onClose();
            setErrors({}); // Reset errors on close
          }}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form style={{ display: "flex", flexDirection: "column", gap: 16 }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
              gap: 4,
            }}
          >
            <FormControl fullWidth error={!!errors.User_Type}>
              <InputLabel>User Type</InputLabel>
              <Select
                name="User_Type"
                value={formData.User_Type}
                onChange={handleChange}
                required
                label="User Type"
              >
                <MenuItem value="">-- Select User Type --</MenuItem>
                <MenuItem value="Partner">Partner</MenuItem>
                <MenuItem value="Admin">Admin</MenuItem>
                <MenuItem value="Super Admin">Super Admin</MenuItem>
                <MenuItem value="Account">Account</MenuItem>
              </Select>
              {errors.User_Type && (
                <span style={{ color: "red", fontSize: "0.75rem", marginTop: "4px" }}>
                  {errors.User_Type}
                </span>
              )}
            </FormControl>

            <InputGroup
              label="User Name"
              name="Name"
              type="text"
              value={formData.Name}
              handleChange={handleChange}
              placeholder="Enter user name"
              disabled={isLoading}
              required
              error={errors.Name} // Display error if exists
            />
            <InputGroup
              label="Email"
              name="Email"
              type="email"
              value={formData.Email}
              handleChange={handleChange}
              placeholder="Enter email"
              disabled={isLoading}
              required
              error={errors.Email} // Display error if exists
            />
            <InputGroup
              label="Number"
              name="Number"
              type="tel"
              value={formData.Number}
              handleChange={handleChange}
              placeholder="Enter mobile number"
              disabled={isLoading}
            />
            <InputGroup
              label="Password"
              name="Password"
              type="password"
              value={formData.Password}
              handleChange={handleChange}
              placeholder="Enter password"
              disabled={isLoading}
              required
              error={errors.Password} // Display error if exists
            />
            <InputGroup
              label="Display Name (Optional)"
              name="DisplayName"
              type="text"
              value={formData.DisplayName}
              handleChange={handleChange}
              placeholder="Enter display name"
              disabled={isLoading}
            />
            <InputGroup
              label="Access Expiration"
              name="AccessExpiration"
              type="date"
              value={formData.AccessExpiration}
              handleChange={handleChange}
              disabled={isLoading}
            />
            <InputGroup
              label="About Me"
              name="AboutMe"
              type="textarea"
              value={formData.AboutMe}
              handleChange={handleChange}
              placeholder="Enter about me description"
              disabled={isLoading}
            />
            <InputGroup
              type="hidden"
              name="CreationDate"
              value={formData.CreationDate}
            />
            <InputGroup
              type="file"
              name="ProfilePic"
              handleChange={handleChange}
              fileStyleVariant="style1"
              label="Profile Picture"
              placeholder="Profile Picture"
            />

            <div style={{ gridColumn: "1 / -1" }}>
              <Checkbox
                label="Active"
                name="Status"
                onChange={handleChange}
                withIcon="check"
                withBg
                radius="md"
                checked={formData.Status === true}
              />
            </div>
          </div>
        </form>
      </DialogContent>

      <DialogActions sx={{ px: 2, py: 2 }}>
        <Button
          label={isLoading ? "Adding..." : "Add User"}
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
          disabled={isLoading}
        />
      </DialogActions>
    </Dialog>
  );
};

const DomainUsersModal = ({ isOpen, onClose, user, fetchUsers }) => {
  const [rows, setRows] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("DomainName");
  const [availableSubdomains, setAvailableSubdomains] = useState([]);
  const [selectedSubdomains, setSelectedSubdomains] = useState([]);
  const [availableDomains, setAvailableDomains] = useState([]);
  const [selectedDomain, setSelectedDomain] = useState(null);
  const [isFetchingSubdomains, setIsFetchingSubdomains] = useState(false);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const columns = [
    { id: "DomainName", label: "Domain" },
    { id: "SubDomainNameWithUrl", label: "Sub Domain" },
    // { id: "CreatedAt", label: "Assigned On" },
  ];

  const fetchDomainData = useCallback(async () => {
    if (!user?.Id) return;

    try {
      setIsLoading(true);

      const response = await axios.get("/api/AssignUserSubDomain/Get", {
        params: {
          Id: user.Id,
          page: page + 1,
          length: rowsPerPage,
          q: debouncedSearchTerm,
          orderBy : orderBy,
          orderDir: order,
        },
        withCredentials: true,
      });

      const data = response.data;

      if (data && data.success && data.data) {
        const formattedRows = data.data.map((row) => ({
          ...row,
          SubDomainNameWithUrl: `${row.SubDomainName} (${row.SubDomainUrl})`,
          CreatedAt: new Date(row.CreatedAt).toLocaleString("en-IN", {
            day: "2-digit",
            month: "short",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
          }),
        }));

        setRows(formattedRows);
        setTotalCount(data.pagination?.recordsFiltered || 0);
      } else {
        setRows([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching subdomains:", error);
      setRows([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  }, [user, page, rowsPerPage, debouncedSearchTerm, order, orderBy]);

  const fetchAvailableDomains = async () => {
    try {
      const response = await axios.get("/api/Domain/GetDropDown", {
        withCredentials: true,
      });

      if (response.data?.success) {
        setAvailableDomains(response.data.data);
        if (user?.DomainId) {
          const defaultDomain = response.data.data.find(
            (d) => d.Id === user.DomainId,
          );
          if (defaultDomain) {
            setSelectedDomain(defaultDomain);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching available domains:", error);
    }
  };

  const fetchAvailableSubdomains = async () => {
    if (!selectedDomain?.Id) {
      setAvailableSubdomains([]);
      return;
    }

    try {
      setIsFetchingSubdomains(true);
      const response = await axios.get("/api/SubDomain/GetDropDown", {
        params: {
          DomainId: selectedDomain.Id,
        },
        withCredentials: true,
      });

      if (response.data?.success) {
        setAvailableSubdomains(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching available subdomains:", error);
    } finally {
      setIsFetchingSubdomains(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchDomainData();
    }
  }, [fetchDomainData, isOpen]);

  useEffect(() => {
    if (isOpen) {
      fetchAvailableDomains();
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && selectedDomain) {
      fetchAvailableSubdomains();
    }
  }, [selectedDomain, isOpen]);

  const handleAssignSubdomains = async () => {
    if (!selectedDomain) {
      Swal.fire({
        title: "No domain selected",
        text: "Please select a domain first",
        icon: "warning",
        confirmButtonColor: "#5750f1",
      });
      return;
    }

    if (selectedSubdomains.length === 0) {
      Swal.fire({
        title: "No selection",
        text: "Please select at least one subdomain to assign",
        icon: "warning",
        confirmButtonColor: "#5750f1",
      });
      return;
    }

    try {
      const response = await axios.post(
        "/api/AssignUserBySubDomain/Add",
        {
          SubDomainId: selectedSubdomains,
          AssignUser: user.Id,
        },
        { withCredentials: true },
      );

      if (response.data?.success) {
        Swal.fire({
          title: "Success",
          text: `Assigned ${response.data.NewMappings} subdomain(s) to user`,
          icon: "success",
          timer: 1500,
          showConfirmButton: false,
        });

        fetchDomainData();
        setSelectedSubdomains([]);
        fetchUsers();
      } else {
        throw new Error(
          response.data?.message || "Failed to assign subdomains",
        );
      }
    } catch (error) {
      console.error("Error assigning subdomains:", error);
      Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          error.message ||
          "Failed to assign subdomains",
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
    } finally {
      setSelectedDomain(null);
    }
  };

  const handleRemoveUser = async (rowData) => {
    try {
      const result = await Swal.fire({
        title: "Are you sure?",
        text: `You want to remove ${rowData.SubDomainName} from this user?`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
        showCloseButton: true,
        confirmButtonText: "Yes, remove it!",
      });

      if (!result.isConfirmed) return;

      setRows((prevRows) => prevRows.filter((r) => r.Id !== rowData.Id));

      Swal.fire({
        title: "Success",
        text: `Subdomain mapping (ID: ${rowData.Id}) deleted successfully`,
        icon: "success",
        timer: 1500,
        showConfirmButton: false,
        customClass: {
          popup: "z-[2000]",
        },
      });

      await axios.delete("/api/SubDomainUserMapping/Delete", {
        data: { Id: rowData.Id },
        withCredentials: true,
      });

      fetchUsers();

      if (typeof onClose === "function") {
        onClose({ deleted: true, type: "domain" });
      }
    } catch (err) {
      console.error("Error removing subdomain mapping:", err);
      await Swal.fire({
        title: "Error",
        text: `Failed to remove subdomain mapping: ${err.message}`,
        icon: "error",
        confirmButtonColor: "#5750f1",
        showCloseButton: true,
        showCancelButton: true,
      });

      fetchDomainData();
    }
  };

  const handleCloseModal = () => {
    setPage(0);
    setSearchTerm("");
    setOrder("asc");
    setOrderBy("");
    setSelectedDomain(null);
    setSelectedSubdomains([]);
    onClose && onClose();
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={handleCloseModal}
      fullWidth
      maxWidth="md"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
      aria-labelledby="domain-users-modal-title"
      aria-describedby="domain-users-modal-description"
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          Domain & SubDomains for{" "}
          <span className="font-bold">{user?.Name || ""}</span>
        </span>
        <IconButton
          aria-label="close"
          onClick={handleCloseModal}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <div className="rounded-lg">
          <div className="flex flex-col items-center md:flex-row md:gap-4">
            <div className="w-full z-50 flex-1">
              <SearchableDropdown
                options={availableDomains}
                label={"Domain"}
                placeholder="Search domains..."
                value={selectedDomain?.Id}
                onChange={(domain) => setSelectedDomain(domain)}
                displayKey="Name"
                displayKey2="ShowUrlName"
                idKey="Id"
              />
            </div>

            <div className="w-full z-30 flex-1">
              <MultiSelectDropdown
                options={availableSubdomains}
                label={"SubDomain"}
                placeholder={
                  selectedDomain
                    ? `Search from ${selectedDomain.Name} subdomains...`
                    : "Please select a domain first"
                }
                value={selectedSubdomains}
                onChange={setSelectedSubdomains}
                displayKey="Name"
                idKey="Id"
                showSelectAll={true}
                disabled={!selectedDomain}
              />
            </div>

            <div className="w-full mt-4 md:w-auto">
              <Button
                type="button"
                label={"Assign Selected"}
                variant="primary"
                shape="rounded"
                onClick={handleAssignSubdomains}
                disabled={
                  !selectedDomain ||
                  selectedSubdomains.length === 0 ||
                  isFetchingSubdomains
                }
                className="w-full"
              />
            </div>
          </div>
          <div className="my-4 w-full sm:w-64">
            <InputGroup
              label="Search"
              placeholder="Search..."
              value={searchTerm}
              handleChange={(e) => {
                setSearchTerm(e.target.value);
                setPage(0);
              }}
              type="text"
              className="w-full"
            />
          </div>
        </div>

        <CustomDataTable
          isLoading={isLoading}
          columns={columns}
          rows={rows}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={setPage}
          onRowsPerPageChange={setRowsPerPage}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onRequestSort={(event, property) => {
            const normalizedProperty = property === "SubDomainNameWithUrl" ? "SubDomainName" : property;
            const isAsc = orderBy === normalizedProperty && order === "asc";
            setOrder(isAsc ? "desc" : "asc");
            setOrderBy(normalizedProperty);
          }}
          onDelete={(row) => handleRemoveUser(row)}
        />
      </DialogContent>
    </Dialog>
  );
};

const StyleIDModal = ({ isOpen, onClose, user, fetchUsers }) => {
  const [rows, setRows] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("");
  const [availableStyleIds, setAvailableStyleIds] = useState([]);
  const [selectedStyleIds, setSelectedStyleIds] = useState([]);
  const [isFetchingStyleIds, setIsFetchingStyleIds] = useState(false);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const columns = [
    { id: "StyleId", label: "Style ID" },
    { id: "Name", label: "Style Id Name" },
    { id: "Prefix", label: "Prefix" },
    { id: "CreatedAt", label: "Assigned On" },
  ];

  const fetchStyleIDs = useCallback(async () => {
    if (!user?.Id) return;

    try {
      setIsLoading(true);
      const response = await axios.get("/api/AssignUserStyleId/Get", {
        params: {
          Id: user.Id,
          page: page + 1,
          length: rowsPerPage,
          q: debouncedSearchTerm,
          orderBy: orderBy,
          orderDir: order,
        },
        withCredentials: true,
      });

      const data = response.data;

      if (data && data.success && data.data) {
        const formattedRows = data.data.map((row) => ({
          ...row,
          CreatedAt: new Date(row.CreatedAt).toLocaleString("en-IN", {
            day: "2-digit",
            month: "short",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
          }),
        }));

        setRows(formattedRows);
        setTotalCount(data.pagination?.recordsFiltered || 0);
      } else {
        setRows([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching style IDs:", error);
      setRows([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  }, [user, page, rowsPerPage, debouncedSearchTerm, order, orderBy]);

  const fetchAvailableStyleIds = async () => {
    try {
      setIsFetchingStyleIds(true);
      const response = await axios.get("/api/StyleIds/getdropdown", {
        withCredentials: true,
      });

      if (response.data?.success) {
        setAvailableStyleIds(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching available style IDs:", error);
    } finally {
      setIsFetchingStyleIds(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchStyleIDs();
    }
  }, [isOpen, fetchStyleIDs]);

  useEffect(() => {
    if (isOpen) {
      fetchAvailableStyleIds();
    }
  }, [isOpen]);

  const handleAssignStyleIds = async () => {
    if (selectedStyleIds.length === 0) {
      Swal.fire({
        title: "No selection",
        text: "Please select at least one Style ID to assign",
        icon: "warning",
        confirmButtonColor: "#5750f1",
      });
      return;
    }

    try {
      const response = await axios.post(
        "/api/AssignUserByStyleId/Add",
        {
          StyleId: selectedStyleIds,
          AssignUser: user.Id,
        },
        { withCredentials: true },
      );

      if (response.data?.success) {
        Swal.fire({
          title: "Success",
          text: `Assigned ${response.data.NewMappings} Style ID(s) to user`,
          icon: "success",
          timer: 1500,
          showConfirmButton: false,
        });

        fetchStyleIDs();
        setSelectedStyleIds([]);
        fetchUsers();
      } else {
        throw new Error(response.data?.message || "Failed to assign Style IDs");
      }
    } catch (error) {
      Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          error.message ||
          "Failed to assign Style IDs",
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
    } finally {
      setSelectedStyleIds([]);
    }
  };

  const handleRemoveUser = async (rowData) => {
    try {
      const result = await Swal.fire({
        title: "Are you sure?",
        text: `You want to remove ${rowData.Name} from this user?`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
        showCloseButton: true,
        confirmButtonText: "Yes, remove it!",
      });

      if (!result.isConfirmed) return;

      setRows((prevRows) => prevRows.filter((r) => r.Id !== rowData.Id));

      Swal.fire({
        title: "Success",
        text: `Style ID mapping (ID: ${rowData.Id}) deleted successfully`,
        icon: "success",
        timer: 1500,
        showConfirmButton: false,
        customClass: {
          popup: "z-[2000]",
        },
      });

      await axios.delete("/api/StyleIdUserMapping/delete", {
        data: { Id: rowData.Id },
        withCredentials: true,
      });

      fetchUsers();

      if (typeof onClose === "function") {
        onClose({ deleted: true, type: "styleId" });
      }
    } catch (err) {
      console.error("Error removing style ID mapping:", err);
      await Swal.fire({
        title: "Error",
        text: `Failed to remove style ID mapping: ${err.message}`,
        icon: "error",
        confirmButtonColor: "#5750f1",
        showCloseButton: true,
        showCancelButton: true,
      });

      fetchStyleIDs();
    }
  };

  const handleCloseModal = () => {
    setPage(0);
    setSearchTerm("");
    setOrder("asc");
    setOrderBy("");
    setSelectedStyleIds([]);
    onClose && onClose();
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={handleCloseModal}
      fullWidth
      maxWidth="md"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
      aria-labelledby="styleid-users-modal-title"
      aria-describedby="styleid-users-modal-description"
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          Style ID for <span className="font-bold">{user?.Name || ""}</span>
        </span>
        <IconButton
          aria-label="close"
          onClick={handleCloseModal}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <div className="rounded-lg">
          <div className="flex mb-2 flex-col items-center md:flex-row md:gap-4">
          <div className="w-full flex-1">
            <InputGroup
              label="Search"
              placeholder="Search..."
              value={searchTerm}
              handleChange={(e) => {
                setSearchTerm(e.target.value);
                setPage(0);
              }}
              type="text"
              className="w-full"
            />
          </div>
            <div className="w-full flex-1">
              <MultiSelectDropdown
                options={availableStyleIds}
                label={"Style IDs"}
                placeholder="Search Style IDs..."
                value={selectedStyleIds}
                onChange={setSelectedStyleIds}
                displayKey="Name"
                idKey="Id"
                showSelectAll={true}
              />
            </div>
            <div className="w-full mt-4 md:w-auto">
              <Button
                type="button"
                label={"Assign Selected"}
                variant="primary"
                shape="rounded"
                onClick={handleAssignStyleIds}
                disabled={selectedStyleIds.length === 0 || isFetchingStyleIds}
                className="w-full"
              />
            </div>

          </div>
         
        </div>

        <CustomDataTable
          isLoading={isLoading}
          columns={columns}
          rows={rows}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={setPage}
          onRowsPerPageChange={setRowsPerPage}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onRequestSort={(event, property) => {
            const isAsc = orderBy === property && order === "asc";
            setOrder(isAsc ? "desc" : "asc");
            setOrderBy(property);
          }}
          onDelete={(row) => handleRemoveUser(row)}
        />
      </DialogContent>
    </Dialog>
  );
};

const PasswordResetModal = ({
  isOpen,
  onClose,
  onSubmit,
  userId,
  isLoading,
}) => {
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  useEffect(() => {
    if (isOpen) {
      setOldPassword("");
      setNewPassword("");
      setConfirmPassword("");
      setPasswordError("");
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!oldPassword) {
      setPasswordError("Old password is required");
      return;
    }
    if (newPassword.length < 8) {
      setPasswordError("New password must be at least 8 characters long");
      return;
    }
    if (newPassword !== confirmPassword) {
      setPasswordError("Passwords don't match");
      return;
    }

    setPasswordError("");
    onSubmit(userId, oldPassword, newPassword);
  };

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      aria-labelledby="responsive-modal-title"
      aria-describedby="responsive-modal-description"
      sx={{ width: "100%" }}
    >
      <Box sx={styleForModal}>
        <div className="mb-5 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-800">
            Reset Password
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
            aria-label="Close modal"
          >
            <IoCloseOutline size={24} />
          </button>
        </div>
        <InputGroup
          label="Current Password"
          type="password"
          name="OldPassword"
          value={oldPassword}
          handleChange={(e) => setOldPassword(e.target.value)}
          placeholder="Enter current password"
          disabled={isLoading}
        />

        <div className="mt-2">
          <InputGroup
            label="New Password"
            type="password"
            name="NewPassword"
            value={newPassword}
            handleChange={(e) => setNewPassword(e.target.value)}
            placeholder="Enter new password"
            disabled={isLoading}
          />
        </div>

        <div className="mt-2">
          <InputGroup
            label="Confirm New Password"
            type="password"
            name="ConfirmPassword"
            value={confirmPassword}
            handleChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Re-enter new password"
            disabled={isLoading}
          />
        </div>
        {passwordError && (
          <Typography color="error" variant="body2" sx={{ mt: 1 }}>
            {passwordError}
          </Typography>
        )}
        <div className="mt-5 flex justify-end">
          <Button
            label={isLoading ? "Processing..." : "Reset Password"}
            variant="primary"
            shape="rounded"
            className="flex-1"
            onClick={handleSubmit}
            disabled={isLoading}
          />
        </div>
      </Box>
    </Modal>
  );
};

const EditUserModal = ({
  isOpen,
  onClose,
  user,
  formData,
  handleFormChange,
  handleEditFormSubmit,
  isLoading,
}) => {
  const [errors, setErrors] = useState({}); // Added state for validation errors

  if (!isOpen || !user) return null;

  const validateForm = () => {
    const newErrors = {};
    const requiredFields = [
      { key: "Name", label: "User Name" },
      { key: "Email", label: "Email" },
      { key: "User_Type", label: "User Type" },
    ];

    requiredFields.forEach(({ key, label }) => {
      if (!formData[key]?.trim()) {
        newErrors[key] = `${label} is required`;
      }
    });

    // Optional: Add email format validation
    if (formData.Email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.Email)) {
      newErrors.Email = "Invalid email format";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    handleFormChange(e); // Call the parent's handleFormChange
    const { name } = e.target;
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return; // Prevent submission and keep modal open if validation fails
    }
    handleEditFormSubmit(e);
  };

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        onClose();
        setErrors({}); // Reset errors on close
      }}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          Edit User Details
        </span>
        <IconButton
          aria-label="close"
          onClick={() => {
            onClose();
            setErrors({}); // Reset errors on close
          }}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form
          onSubmit={handleSubmit}
          style={{ display: "flex", flexDirection: "column", gap: 16 }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
              gap: 20,
            }}
          >
            <FormControl fullWidth error={!!errors.User_Type}>
              <InputLabel>User Type</InputLabel>
              <Select
                name="User_Type"
                value={formData.User_Type || ""}
                onChange={handleChange}
                required
                label="User Type"
              >
                <MenuItem value="">-- Select User Type --</MenuItem>
                <MenuItem value="Partner">Partner</MenuItem>
                <MenuItem value="Admin">Admin</MenuItem>
                <MenuItem value="Super Admin">Super Admin</MenuItem>
                <MenuItem value="Account">Account</MenuItem>
              </Select>
              {errors.User_Type && (
                <span style={{ color: "red", fontSize: "0.75rem", marginTop: "4px" }}>
                  {errors.User_Type}
                </span>
              )}
            </FormControl>

            <InputGroup
              label="User Name"
              name="Name"
              type="text"
              value={formData.Name || ""}
              handleChange={handleChange}
              placeholder="Enter user name"
              disabled={isLoading}
              required
              error={errors.Name} // Display error if exists
            />

            <InputGroup
              label="Email"
              name="Email"
              type="email"
              value={formData.Email || ""}
              handleChange={handleChange}
              placeholder="Enter email"
              disabled={isLoading}
              required
              error={errors.Email} // Display error if exists
            />

            <InputGroup
              label="Number"
              name="Number"
              type="tel"
              value={formData.Number || ""}
              handleChange={handleChange}
              placeholder="Enter mobile number"
              disabled={isLoading}
            />

            <InputGroup
              label="Display Name (Optional)"
              name="DisplayName"
              type="text"
              value={formData.DisplayName || ""}
              handleChange={handleChange}
              placeholder="Enter display name"
              disabled={isLoading}
            />

            <InputGroup
              label="Access Expiration"
              type="date"
              name="AccessExpiration"
              value={
                formData.AccessExpiration
                  ? new Date(formData.AccessExpiration)
                      .toISOString()
                      .slice(0, 10)
                  : ""
              }
              handleChange={handleChange}
              disabled={isLoading}
            />
            <InputGroup
              label="About Me"
              name="AboutMe"
              type="textarea"
              value={formData.AboutMe}
              handleChange={handleChange}
              placeholder="Enter about me description"
              disabled={isLoading}
            />
            <InputGroup
              type="file"
              name="ProfilePic"
              handleChange={handleChange}
              fileStyleVariant="style1"
              label="Profile Picture"
              placeholder="Profile Picture"
            />

            <div style={{ gridColumn: "1 / -1" }}>
              <Checkbox
                label="Active"
                name="Status"
                onChange={handleChange}
                withIcon="check"
                withBg
                radius="md"
                checked={formData.Status === true}
              />
            </div>
          </div>
        </form>
      </DialogContent>

      <DialogActions sx={{ px: 2, py: 2 }}>
        <Button
          label={isLoading ? "Updating..." : "Save"}
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
          disabled={isLoading}
        />
      </DialogActions>
    </Dialog>
  );
};

const AllUser = () => {
  const [users, setUsers] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [isResetModalOpen, setIsResetModalOpen] = useState(false);
  const [isDomainUsersModalOpen, setIsDomainUsersModalOpen] = useState(false);
  const [domainUser, setDomainUser] = useState({});
  const [isStyleIDModalOpen, setIsStyleIDModalOpen] = useState(false);
  const [selectedUserForStyleID, setSelectedUserForStyleID] = useState(null);
  const [editingUser, setEditingUser] = useState(null);
  const [resetUserId, setResetUserId] = useState(null);
  const initialForm = {
    Name: "",
    Email: "",
    Number: "",
    User_Type: "",
    Password: "",
    DisplayName: "",
    ProfilePic: null,
    AccessExpiration: "",
    Status: false,
    AboutMe: "",
  };
  const [formData, setFormData] = useState(initialForm);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const columns = [
    { id: "Name", label: "User Name" },
    { id: "Email", label: "Email" },
    { id: "Number", label: "Contact No" },
    { id: "subDomainCount", label: "Domain" },
    { id: "styleIdCount", label: "Style ID" },
    { id: "Status", label: "Status" },
  ];

  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await axios.get("/api/adminuser/get", {
        params: {
          page: page + 1,
          length: rowsPerPage,
          q: debouncedSearchTerm,
          orderBy,
          orderDir: order,
        },
      });

      const data = response.data;

      if (data?.data) {
        setUsers(data.data);
        setTotalCount(data.pagination?.recordsFiltered || 0);

        if (
          page > 0 &&
          data.data.length === 0 &&
          data.pagination.recordsFiltered > 0
        ) {
          setPage(0);
        }
      } else {
        setUsers([]);
        setTotalCount(0);
      }
    } catch (err) {
      console.error("Fetch error:", err.message);
      setUsers([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  }, [page, rowsPerPage, debouncedSearchTerm, order, orderBy]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleEdit = async (Id) => {
    try {
      Swal.fire({
        title: "Loading User Data",
        html: "Please wait while we fetch user details...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      const response = await axios.get(`/api/adminuser/GetById?id=${Id}`, {
        withCredentials: true,
      });
      const user = response.data.data[0];

      let creationDate = user.CreationDate;
      if (!creationDate && user.AccessExpiration) {
        const expirationDate = new Date(user.AccessExpiration);
        const estimatedCreationDate = new Date(expirationDate);
        estimatedCreationDate.setFullYear(expirationDate.getFullYear() - 1);
        creationDate = estimatedCreationDate.toISOString().split("T")[0];
      } else if (!creationDate) {
        creationDate = new Date().toISOString().split("T")[0];
      }

      const userWithDefaults = {
        Id: user.Id,
        Name: user.Name || "",
        Email: user.Email || "",
        Number: user.Number || "",
        User_Type: user.User_Type || "",
        DisplayName: user.DisplayName || "",
        ProfilePic: user.ProfilePic || "",
        AccessExpiration: user.AccessExpiration || "",
        CreationDate: creationDate,
        Status: user.Status === true || user.Status === "true",
        AboutMe: user.AboutMe || "",
      };

      setEditingUser(userWithDefaults);
      setFormData({
        Name: userWithDefaults.Name,
        Email: userWithDefaults.Email,
        Number: userWithDefaults.Number,
        User_Type: userWithDefaults.User_Type,
        DisplayName: userWithDefaults.DisplayName,
        ProfilePic: user.ProfilePic || "",
        AccessExpiration: userWithDefaults.AccessExpiration,
        CreationDate: userWithDefaults.CreationDate,
        Status: userWithDefaults.Status,
        AboutMe: userWithDefaults.AboutMe,
      });

      Swal.close();
      setIsEditModalOpen(true);
    } catch (error) {
      console.error("Error fetching user data:", error);

      Swal.close();
      await Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          "Failed to load user data. Please try again.",
        icon: "error",
        confirmButtonText: "OK",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  const handleBlock = async (Id) => {
    try {
      const user = users.find((user) => user.Id === Id);
      if (!user) {
        throw new Error("User not found");
      }

      const isCurrentlyBlocked = Boolean(user.Block);
      const newBlockStatus = !isCurrentlyBlocked;

      const confirmResult = await Swal.fire({
        title: "Are you sure?",
        text: `Do you want to ${newBlockStatus ? "block" : "unblock"} ${user.Name}?`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: `Yes, ${newBlockStatus ? "block" : "unblock"}!`,
        cancelButtonText: "Cancel",
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
      });

      if (!confirmResult.isConfirmed) return;

      const response = await axios.patch("/api/adminuser/block", {
        Id,
        Block: newBlockStatus,
      });

      if (response.status !== 200) {
        throw new Error(
          response.data?.message || "Failed to update user status",
        );
      }

      setUsers((prevUsers) =>
        prevUsers.map((u) =>
          u.Id === Id ? { ...u, Block: newBlockStatus } : u,
        ),
      );

      await Swal.fire({
        title: "Success!",
        text: `${user.Name} has been ${newBlockStatus ? "blocked" : "unblocked"} successfully.`,
        icon: "success",
        confirmButtonColor: "#5750f1",
      });
    } catch (error) {
      console.error("Error updating user status:", error);

      await Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          error.message ||
          "Failed to update user status",
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  const handleDelete = async (Id) => {
    try {
      const result = await Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
      });

      if (!result.isConfirmed) return;

      const userToDelete = users.find((u) => u.Id === Id);
      if (!userToDelete) {
        console.error("User not found for deletion");
        return;
      }

      setUsers((prevUsers) => prevUsers.filter((u) => u.Id !== Id));

      await Swal.fire({
        title: "Deleted!",
        text: "User deleted successfully",
        icon: "success",
        confirmButtonColor: "#5750f1",
      });

      const response = await axios.delete("/api/adminuser/delete", {
        data: { Id },
      });

      if (response.status !== 200) {
        throw new Error(`Failed with status: ${response.status}`);
      }
    } catch (error) {
      console.error("Error deleting user:", error);

      setUsers((prevUsers) =>
        [...prevUsers, userToDelete].sort((a, b) =>
          a.Name.localeCompare(b.Name),
        ),
      );

      await Swal.fire({
        title: "Error",
        text: `Failed to delete user: ${error.message}`,
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  const handleResetSubmit = async (row, newPassword, oldPassword) => {
    let userId = row.Id;
    try {
      // Show confirmation dialog first
      const { isConfirmed } = await Swal.fire({
        title: "Confirm Password Reset",
        text: `Are you sure you want to reset the password for user : ${row.Name}?`,
        icon: "question",
        showCancelButton: true,
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, reset password",
        cancelButtonText: "Cancel",
      });

      // If user cancels, exit the function
      if (!isConfirmed) {
        return false;
      }

      // Show loading indicator while API call is in progress
      Swal.fire({
        title: "Processing",
        text: "Resetting password...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      // Make the API call
      const response = await axios.post("/api/adminuser/ResetPassword", {
        userId,
        oldPassword,
        newPassword,
      });

      // Close loading indicator
      Swal.close();

      if (response.data.success) {
        await Swal.fire({
          title: "Success",
          text:
            response.data.message ||
            `Password reset successfully for user ID: ${userId}`,
          icon: "success",
          confirmButtonColor: "#5750f1",
        });
        setIsResetModalOpen(false);
        return true;
      } else {
        throw new Error(`Server responded with status ${response.status}`);
      }
    } catch (error) {
      Swal.close();

      console.error("Error resetting password:", error);
      await Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          "Failed to reset password. Please try again.",
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
      return false;
    }
  };


  const handleDomainClick = (user) => {
    setDomainUser(user);
    setIsDomainUsersModalOpen(true);
  };

  const handleStyleIDClick = (user) => {
    setSelectedUserForStyleID(user);
    setIsStyleIDModalOpen(true);
  };



  const generateUniqueEmail = () => {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    return `user_${timestamp}_${randomString}@example.com`;
  };

  const handleAddUser = async (newUserData) => {
    try {
      const uniqueEmail = newUserData.Email || generateUniqueEmail();

      const creationDate = new Date();
      const expirationDate = newUserData.AccessExpiration
        ? new Date(newUserData.AccessExpiration).toISOString()
        : new Date(
            creationDate.setFullYear(creationDate.getFullYear() + 1),
          ).toISOString();

      const formData = new FormData();
      formData.append("Name", newUserData.Name || "New User");
      formData.append("Email", uniqueEmail);
      formData.append("Number", newUserData.Number || "");
      formData.append("User_Type", newUserData.User_Type || "Account");
      formData.append(
        "Password",
        newUserData.Password || "DefaultPassword123!",
      );
      formData.append(
        "DisplayName",
        newUserData.DisplayName || newUserData.Name || "New User",
      );
      formData.append("AccessExpiration", expirationDate);
      formData.append("Status", newUserData.Status === true ? "true" : "false");
      formData.append("AboutMe", newUserData.AboutMe || "");
      if (newUserData.ProfilePic && newUserData.ProfilePic instanceof File) {
        formData.append("ProfilePic", newUserData.ProfilePic);
      }

      // const { isConfirmed } = await Swal.fire({
      //   title: "Confirm",
      //   text: `Are you sure you want to add this user with status: ${newUserData.Status ? "Active" : "Inactive"}?`,
      //   icon: "question",
      //   showCancelButton: true,
      //   confirmButtonColor: "#5750f1",
      //   cancelButtonColor: "#d33",
      //   confirmButtonText: "Yes, add user",
      // });

      // if (!isConfirmed) return;

      setIsAddUserModalOpen(false);

      const tempId = Date.now();
      setUsers((prev) => [
        ...prev,
        { Id: tempId, ...newUserData, Email: uniqueEmail },
      ]);

      await Swal.fire({
        title: "Success",
        text: "User added to the interface. The system will automatically save it to the server.",
        icon: "success",
        confirmButtonColor: "#5750f1",
      });

      const saveUser = async (formDataToSend) => {
        try {
          const response = await axios.post(
            "/api/adminuser/add",
            formDataToSend,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
              withCredentials: true,
            },
          );

          if (response.data?.success && response.data?.data?.Id) {
            setUsers((prev) =>
              prev.map((user) =>
                user.Id === tempId
                  ? { ...user, Id: response.data.data.Id }
                  : user,
              ),
            );
            fetchUsers();
          }
        } catch (err) {
          if (axios.isAxiosError(err) && err.response?.status === 409) {
            console.warn("Email already exists. Retrying...");
            const retryEmail = generateUniqueEmail();
            formDataToSend.set("Email", retryEmail);
            setUsers((prev) =>
              prev.map((user) =>
                user.Id === tempId ? { ...user, Email: retryEmail } : user,
              ),
            );
            await saveUser(formDataToSend);
          } else {
            console.error("Error saving user:", err);
            await Swal.fire({
              title: "Error",
              text: `Server error: ${err.message || "Unknown error"}`,
              icon: "error",
              confirmButtonColor: "#5750f1",
            });
            setUsers((prev) => prev.filter((user) => user.Id !== tempId));
          }
        }
      };

      await saveUser(formData);
    } catch (error) {
      console.error("Error in add user process:", error);
      await Swal.fire({
        title: "Error",
        text: "Failed to add user: " + error.message,
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setEditingUser(null);
    setFormData({
      Name: "",
      Email: "",
      Number: "",
      User_Type: "",
      Password: "",
      DisplayName: "",
      ProfilePic: null,
      AccessExpiration: "",
      Status: false,
    });
  };

  const handleAddModalClose = () => {
    setIsAddUserModalOpen(false);
    setFormData({
      Name: "",
      Email: "",
      Number: "",
      User_Type: "",
      Password: "",
      DisplayName: "",
      ProfilePic: null,
      AccessExpiration: "",
      Status: false,
    });
  };

  const handleFormChange = (e) => {
    const { name, value, files, type, checked } = e.target;

    let newValue;
    if (type === "checkbox") {
      newValue = checked;
    } else if (type === "file") {
      newValue = files?.[0] || null;
    } else if (name === "Number") {
      if (value && !/^\d{0,10}$/.test(value)) {
        return;
      }
      newValue = value;
    } else {
      newValue = value;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: newValue,
    }));
  };

  const handleEditFormSubmit = async (e) => {
    e.preventDefault();

    const { isConfirmed } = await Swal.fire({
      title: "Confirm Update",
      text: `Are you sure you want to update this user with status: ${formData.Status ? "Active" : "Inactive"}?`,
      icon: "question",
      showCloseButton: true,
      showCancelButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, update user",
    });

    if (!isConfirmed) return;

    try {
      const fd = new FormData();
      fd.append("Id", editingUser.Id);
      fd.append("Name", formData.Name || "");
      fd.append("Email", formData.Email || "");
      fd.append("User_Type", formData.User_Type || "");
      fd.append("Number", formData.Number || "");
      fd.append("Status", formData.Status ? "true" : "false");
      fd.append("DisplayName", formData.DisplayName || "");
      fd.append("AccessExpiration", formData.AccessExpiration || "");
      fd.append("AboutMe", formData.AboutMe || "");
      if (formData.ProfilePic instanceof File) {
        fd.append("ProfilePic", formData.ProfilePic);
      }

      const response = await axios.put("/api/adminuser/edit", fd, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        withCredentials: true,
      });

      if (response.data.success) {
        setUsers((prevUsers) =>
          prevUsers.map((user) =>
            user.Id === editingUser.Id ? { ...user, ...formData } : user,
          ),
        );
        handleEditModalClose();
        await Swal.fire({
          title: "Success",
          text: "User updated successfully",
          icon: "success",
          confirmButtonColor: "#5750f1",
        });
      } else {
        throw new Error(response.data.error || "Failed to update user");
      }
    } catch (error) {
      console.error("Error updating user:", error);
      await Swal.fire({
        title: "Error",
        text:
          error.response?.data?.error ||
          error.response?.data?.message ||
          error.message ||
          "Failed to update user",
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  return (
    <div className="w-full min-h-screen relative overflow-x-hidden font-poppins bg-gray-50">
      <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="p-6">
          <h1 className="text-white text-2xl font-bold">User Management</h1>
        </div>
      </div>

      <div className="absolute w-[95%] bg-white dark:bg-gray-dark shadow-lg rounded-[10px] left-1/2 -translate-x-1/2 top-[90px] border border-stroke dark:border-dark-3 p-4 sm:p-6">
        <div className="mb-5 ml-auto flex flex-col gap-4 flex-wrap sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-col flex-wrap gap-4 items-center sm:flex-row">
            <div className="w-full mt-4 sm:w-64">
              <Button
                type="button"
                label="Add New User"
                variant="primary"
                shape="rounded"
                icon={<FaPlus size={14} />}
                onClick={() => setIsAddUserModalOpen(true)}
                className="ml-auto flex w-full items-center justify-center gap-2 sm:w-64"
              />
            </div>
            <div className="w-full sm:w-64">
              <InputGroup
                label="Search"
                placeholder="Search..."
                value={searchTerm}
                handleChange={(e) => {
                  setSearchTerm(e.target.value);
                  setPage(0);
                }}
                type="text"
                className="w-full"
              />
            </div>
          </div>
        </div>
        <CustomDataTable
          isLoading={isLoading}
          columns={columns}
          rows={users}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={setPage}
          onRowsPerPageChange={setRowsPerPage}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onRequestSort={(event, property) => {
            const isAsc = orderBy === property && order === "asc";
            setOrder(isAsc ? "desc" : "asc");
            setOrderBy(property);
          }}
          handleSubDomainClick={(row) => handleDomainClick(row)}
          handleStyleIDClick={(row) => handleStyleIDClick(row)}
          onReset={(row) => {handleResetSubmit(row)}}
          onBlock={(row) => handleBlock(row.Id)}
          onEdit={(row) => handleEdit(row.Id)}
          onDelete={(row) => handleDelete(row.Id)}
        />
        <EditUserModal
          isOpen={isEditModalOpen}
          onClose={handleEditModalClose}
          user={editingUser}
          formData={formData}
          isLoading={isLoading}
          handleFormChange={handleFormChange}
          handleEditFormSubmit={handleEditFormSubmit}
        />

        <PasswordResetModal
          isOpen={isResetModalOpen}
          onClose={() => {
            setIsResetModalOpen(false);
            setResetUserId(null);
          }}
          isLoading={isLoading}
          onSubmit={handleResetSubmit}
          userId={resetUserId}
        />

        <AddNewUserModal
          isOpen={isAddUserModalOpen}
          onClose={handleAddModalClose}
          onSubmit={handleAddUser}
          isLoading={isLoading}
        />

        <DomainUsersModal
          isOpen={isDomainUsersModalOpen}
          onClose={() => {
            setIsDomainUsersModalOpen(false);
            setDomainUser({});
          }}
          user={domainUser}
          fetchUsers={fetchUsers}
        />
        <StyleIDModal
          isOpen={isStyleIDModalOpen}
          onClose={() => {
            setSelectedUserForStyleID(null);
            setIsStyleIDModalOpen(false);
          }}
          user={selectedUserForStyleID}
          fetchUsers={fetchUsers}
        />
      </div>
    </div>
  );
};

export default AllUser;