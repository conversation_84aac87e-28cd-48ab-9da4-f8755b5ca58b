import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const domainId = searchParams.get("domainId");
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "asc";

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }
        if (!domainId) {
            return NextResponse.json(
                { error: "DomainId parameter is required" },
                { status: 400 }
            );
        }

        // Base filter for SubDomains
        const baseFilter: any = {
            Domain: domainId,
            IsDeleted: false
        };

        // Add search conditions if provided
        if (search && search !== "") {
            baseFilter.OR = [
                { Name: { contains: search, mode: 'insensitive' } }, // Search SubDomain name
                {
                    SubDomainUserMappings: {
                        some: {
                            AdminUser: {
                                OR: [
                                    { Name: { contains: search, mode: 'insensitive' } },
                                    { Email: { contains: search, mode: 'insensitive' } },
                                    { User_Type: { contains: search, mode: 'insensitive' } }
                                ]
                            }
                        }
                    }
                }
            ];
        }

        // Determine orderBy for Prisma
        let orderByField = {};
        if (orderBy === "SubDomainName") {
            orderByField = { Name: orderDir.toLowerCase() };
        } else if (orderBy === "UserName") {
            // We'll handle user sorting after fetching since it's a relation
        } else {
            orderByField = { [orderBy]: orderDir.toLowerCase() };
        }

        // Get counts
        const recordsFiltered = await prisma.subDomain.count({
            where: baseFilter
        });

        // Get all SubDomains with their user mappings
        const SubDomains = await prisma.subDomain.findMany({
            where: baseFilter,
            skip,
            take: limit,
            orderBy: Object.keys(orderByField).length > 0 ? orderByField : undefined,
            include: {
                SubDomainUserMappings: {
                    include: {
                        AdminUser: {
                            select: {
                                Id: true,
                                Name: true,
                                Email: true,
                                User_Type: true
                            }
                        }
                    }
                }
            }
        });

        // Transform data to separate each user
        let transformedData = SubDomains.flatMap(SubDomain =>
            SubDomain.SubDomainUserMappings.map(mapping => ({
                Id: mapping.Id,
                SubDomainId: SubDomain.Id,
                SubDomainName: SubDomain.Name,
                CreatedAt: SubDomain.CreatedAt,
                UserId: mapping.AdminUser?.Id,
                UserName: mapping.AdminUser?.Name,
                UserEmail: mapping.AdminUser?.Email,
                UserType: mapping.AdminUser?.User_Type
            }))
        );

        // Apply sorting for UserName if needed (since it's a relation)
        if (orderBy === "UserName") {
            transformedData.sort((a, b) =>
                orderDir.toLowerCase() === 'asc'
                    ? (a.UserName ?? "").localeCompare(b.UserName ?? "")
                    : (b.UserName ?? "").localeCompare(a.UserName ?? "")
            );
        }

        const recordsTotal = await prisma.subDomainUserMappings.count({
            where: {
                SubDomain: {
                    Domain: domainId,
                    IsDeleted: false
                }
            }
        });
        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                //recordsTotal,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}