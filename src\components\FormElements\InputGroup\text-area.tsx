// import { cn } from "@/lib/utils";
// import { useId, useState, useRef, useEffect } from "react";

// interface PropsType {
//   label: string;
//   name?: string;
//   value?: string;
//   handleChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
//   placeholder?: string;
//   required?: boolean;
//   disabled?: boolean;
//   active?: boolean;
//   className?: string;
//   icon?: React.ReactNode;
//   defaultValue?: string;
//   rows?: number;
// }

// export function TextAreaGroup({
//   label,
//   name,
//   value,
//   handleChange,
//   placeholder = "",
//   required,
//   disabled,
//   active,
//   className,
//   icon,
//   defaultValue,
//   rows = 6,
// }: PropsType) {
//   const id = useId();
//   const [isFocused, setIsFocused] = useState(false);
//   const [hasValue, setHasValue] = useState(false);
//   const textareaRef = useRef<HTMLTextAreaElement>(null);

//   useEffect(() => {
//     if (value || defaultValue) {
//       setHasValue(true);
//     }
//   }, [value, defaultValue]);

//   const handleFocus = () => {
//     setIsFocused(true);
//   };

//   const handleBlur = () => {
//     setIsFocused(false);
//   };

//   const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
//     setHasValue(!!e.target.value);
//     if (handleChange) {
//       handleChange(e);
//     }
//   };

//   return (
//     <div className={cn("relative pt-5", className)}>
//       <div className="relative [&_svg]:pointer-events-none [&_svg]:absolute [&_svg]:left-4 [&_svg]:top-4">
//         <textarea
//           ref={textareaRef}
//           id={id}
//           rows={rows}
//           name={name}
//           value={value}
//           onChange={handleTextareaChange}
//           onFocus={handleFocus}
//           onBlur={handleBlur}
//           placeholder={isFocused ? placeholder : ''}
//           defaultValue={defaultValue}
//           className={cn(
//             "w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-4 py-3 text-dark outline-none transition-all focus:border-primary disabled:cursor-default disabled:bg-gray-2 data-[active=true]:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary dark:disabled:bg-dark dark:data-[active=true]:border-primary",
//             icon && "pl-12 pr-4",
//           )}
//           required={required}
//           disabled={disabled}
//           data-active={active}
//         />

//         <label
//           htmlFor={id}
//           className={cn(
//             "absolute left-4 top-6 z-10 -translate-y-3 cursor-text text-dark-6 transition-all duration-200 dark:text-white",
//             (isFocused || hasValue) && 
//               "left-3 top-1 text-xs text-primary bg-white px-1 dark:bg-dark-2",
//             isFocused && "text-primary",
//             icon && "left-12",
//           )}
//           onClick={() => textareaRef.current?.focus()}
//         >
//           {label}
//           {required && <span className="ml-1 select-none text-red">*</span>}
//         </label>

//         {icon}
//       </div>
//     </div>
//   );
// }

import { cn } from "@/lib/utils";
import { useId, useState, useRef, useEffect } from "react";
import { BsInfoCircleFill } from "react-icons/bs";

interface PropsType {
  label: string;
  name?: string;
  value?: string;
  handleChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  active?: boolean;
  className?: string;
  icon?: React.ReactNode;
  defaultValue?: string;
  rows?: number;
  showInformationIcon?: boolean;
  informationText?: string;
}

export function TextAreaGroup({
  label,
  name,
  value,
  handleChange,
  placeholder = "",
  required,
  disabled,
  active,
  className,
  icon,
  defaultValue,
  rows = 6,
  showInformationIcon = false,
  informationText,
}: PropsType) {
  const id = useId();
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const tooltipTimeoutRef = useRef<number | null>(null);

  useEffect(() => {
    if (value || defaultValue) {
      setHasValue(true);
    }
    return () => {
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
    };
  }, [value, defaultValue]);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setHasValue(!!e.target.value);
    handleChange?.(e);
  };

  const handleMouseEnter = () => {
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
      tooltipTimeoutRef.current = null;
    }
    setShowTooltip(true);
  };

  const handleMouseLeave = () => {
    tooltipTimeoutRef.current = window.setTimeout(() => {
      setShowTooltip(false);
    }, 200);
  };

  return (
    <div className={cn("relative pt-5", className)}>
      <div className="relative">
        <textarea
          ref={textareaRef}
          id={id}
          rows={rows}
          name={name}
          value={value}
          onChange={handleTextareaChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={isFocused ? placeholder : ""}
          defaultValue={defaultValue}
          className={cn(
            "w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-4 py-3 pr-12 text-dark outline-none transition-all focus:border-primary disabled:cursor-default disabled:bg-gray-2 dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary dark:disabled:bg-dark",
            icon && "pl-12"
          )}
          required={required}
          disabled={disabled}
          data-active={active}
        />

        {/* Floating label */}
        <label
          htmlFor={id}
          className={cn(
            "absolute left-4 top-6 z-10 -translate-y-3 cursor-text text-dark-6 transition-all duration-200 dark:text-white",
            (isFocused || hasValue) &&
              "left-3 top-1 text-xs text-primary bg-white px-1 dark:bg-dark-2",
            icon && "left-12"
          )}
          onClick={() => textareaRef.current?.focus()}
        >
          {label}
          {required && <span className="ml-1 select-none text-red">*</span>}
        </label>

        {/* Icon inside textarea on right */}
        {showInformationIcon && informationText && (
          <div
            className="absolute right-4 top-8 -translate-y-1/2 z-20"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600 focus:outline-none"
              onClick={(e) => {
                e.preventDefault();
                setShowTooltip(!showTooltip);
              }}
              aria-label="More information"
            >
              <BsInfoCircleFill className="h-4 w-4" />
            </button>

            {showTooltip && (
              <div className="absolute right-0 top-full mt-2 w-64 rounded-md bg-gray-800 p-2 text-xs text-white shadow-lg">
                {informationText}
                <div className="absolute -top-1 right-3 h-2 w-2 rotate-45 transform bg-gray-800"></div>
              </div>
            )}
          </div>
        )}

        {/* Optional left icon */}
        {icon && (
          <div className="absolute left-4 top-4">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
}
