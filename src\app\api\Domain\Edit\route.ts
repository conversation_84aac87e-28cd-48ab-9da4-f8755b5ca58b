import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { v4 as uuidv4 } from 'uuid';
import { writeFile, access, constants, mkdir, unlink } from 'fs/promises';
import path from 'path';
import { existsSync } from 'fs';
import sharp from 'sharp';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || '/var/www/images';
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MIN_FILE_SIZE = 1024; // 1KB minimum file size

type SharpImageFormat = keyof sharp.FormatEnum;

// Type guard to check if FormDataEntryValue is a File
function isFile(value: FormDataEntryValue | null): value is File {
    return value !== null && typeof value === 'object' && 'size' in value && 'type' in value && 'name' in value;
}

export async function PUT(req: NextRequest) {
    try {
        // Authentication
        type AuthenticatedUser = {
            Id: string;
        };
        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get form data
        const formData = await req.formData();
        const id = formData.get('Id')?.toString();

        if (!id) {
            return NextResponse.json(
                { error: 'Id is required for updating a Domain' },
                { status: 400 }
            );
        }

        // Get existing domain
        const existingDomain = await prisma.domain.findFirst({
            where: { Id: id, IsDeleted: false }
        });


        if (!existingDomain) {
            return NextResponse.json(
                { error: "Domain not found or you don't have permission to update it" },
                { status: 404 }
            );
        }

        const newName = formData.get('Name')?.toString();
        if (newName && newName !== existingDomain.Name) {
            const nameConflict = await prisma.domain.findFirst({
                where: {
                    Name: newName,
                    IsDeleted: false,
                    NOT: { Id: id }
                }
            });
            if (nameConflict) {
                return NextResponse.json({ error: 'Name already exists' }, { status: 409 });
            }
        }

        const newShowUrlName = formData.get('ShowUrlName')?.toString();
        if (newShowUrlName && newShowUrlName !== existingDomain.ShowUrlName) {
            const showUrlConflict = await prisma.domain.findFirst({
                where: {
                    ShowUrlName: newShowUrlName,
                    IsDeleted: false,
                    NOT: { Id: id }
                }
            });
            if (showUrlConflict) {
                return NextResponse.json({ error: 'ShowUrlName already exists' }, { status: 409 });
            }
        }

        try {
            if (!existsSync(UPLOAD_DIR)) {
                console.log('Creating upload directory:', UPLOAD_DIR);
                await mkdir(UPLOAD_DIR, { recursive: true });
            }

            // Test write permissions
            await access(UPLOAD_DIR, constants.W_OK);
        } catch (err) {
            console.error('Upload directory error:', err);
            return NextResponse.json(
                { error: `Server configuration error: ${err instanceof Error ? err.message : 'upload directory not accessible'}` },
                { status: 500 }
            );
        }

        // Helper functions for form data
        const getString = (key: string, fallback: string | null) => {
            const value = formData.get(key);
            return value !== null && value !== '' ? value.toString() : fallback;
        };

        const getNumber = (key: string, fallback: number | null) => {
            const value = formData.get(key);
            return value !== null && value !== '' ? Number(value) : fallback;
        };

        const getBigInt = (key: string, fallback: bigint | null): bigint | null => {
            const value = formData.get(key);
            return value !== null && value !== '' ? BigInt(value.toString()) : fallback;
        };

        // Helper function to validate image formats
        function isSharpImageFormat(format: string): format is SharpImageFormat {
            const sharpFormats: SharpImageFormat[] = ['jpeg', 'png', 'webp', 'gif', 'tiff', 'raw'];
            return sharpFormats.includes(format as SharpImageFormat);
        }

        // Helper function to delete old image files
        const deleteOldImageFiles = async (oldFileName: string | null) => {
            if (!oldFileName) return;

            try {
                const baseName = oldFileName.replace(/\.[^/.]+$/, ''); // Remove extension
                const extension = oldFileName.split('.').pop();

                const filesToDelete = [
                    oldFileName, // original
                    `${baseName}_small.${extension}`, // small version
                    `${baseName}_medium.${extension}` // medium version
                ];

                await Promise.all(
                    filesToDelete.map(async (fileName) => {
                        const filePath = path.join(UPLOAD_DIR, fileName);
                        try {
                            if (existsSync(filePath)) {
                                await unlink(filePath);
                            }
                        } catch (deleteError) {
                            console.warn(`Could not delete old file ${fileName}:`, deleteError);
                            // Don't throw error here - it's not critical if old files can't be deleted
                        }
                    })
                );
            } catch (error) {
                console.warn('Error deleting old image files:', error);
            }
        };

        // Enhanced image processor with validation
        const processImage = async (file: File | null, existingPath: string | null, prefix: string): Promise<string | null> => {
            // If no file or empty file, return existing path
            if (!file || file.size === 0) {
                return existingPath;
            }

            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                let mimeType = file.type;
                let originalExtension: SharpImageFormat = 'jpeg'; // default fallback

                // Handle cases where browser doesn't detect MIME type
                if (!mimeType || mimeType === 'application/octet-stream' || mimeType === '') {
                    try {
                        const metadata = await sharp(buffer).metadata();
                        if (metadata.format && isSharpImageFormat(metadata.format)) {
                            mimeType = `image/${metadata.format}`;
                            originalExtension = metadata.format;
                        } else {
                            console.warn(`Invalid file format for ${prefix}. Keeping existing image.`);
                            return existingPath;
                        }
                    } catch (sharpError) {
                        console.error('Sharp metadata error:', sharpError);
                        console.warn(`Could not process ${prefix} image. Keeping existing image.`);
                        return existingPath;
                    }
                } else {
                    const ext = mimeType.split('/')[1];
                    if (ext && isSharpImageFormat(ext)) {
                        originalExtension = ext;
                    } else {
                        console.warn(`Invalid file type: ${mimeType}. Keeping existing image.`);
                        return existingPath;
                    }
                }

                // Validate file type
                if (!ALLOWED_IMAGE_TYPES.includes(mimeType)) {
                    console.warn(`Invalid file type: ${mimeType}. Allowed types:`, ALLOWED_IMAGE_TYPES, 'Keeping existing image.');
                    return existingPath;
                }

                // Validate file size
                if (file.size < MIN_FILE_SIZE) {
                    console.warn(`File too small: ${file.size} bytes (minimum: ${MIN_FILE_SIZE}). Keeping existing image.`);
                    return existingPath;
                }

                const uniqueId = uuidv4();
                const baseFileName = `${prefix}_${uniqueId}.${originalExtension}`;
                const smallFileName = `${prefix}_${uniqueId}_small.${originalExtension}`;
                const mediumFileName = `${prefix}_${uniqueId}_medium.${originalExtension}`;


                // Process image versions in parallel
                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                const filePaths = [
                    { path: path.join(UPLOAD_DIR, baseFileName), buffer: originalBuffer },
                    { path: path.join(UPLOAD_DIR, smallFileName), buffer: smallBuffer },
                    { path: path.join(UPLOAD_DIR, mediumFileName), buffer: mediumBuffer }
                ];
                // Save all versions
                await Promise.all(
                    filePaths.map(async ({ path: filePath, buffer }) => {
                        try {
                            await writeFile(filePath, buffer);
                        } catch (writeError) {
                            console.error(`Failed to write ${filePath}:`, writeError);
                            throw writeError;
                        }
                    })
                );

                // Verify files were written
                for (const { path: filePath } of filePaths) {
                    if (!existsSync(filePath)) {
                        console.error(`File verification failed: ${filePath} does not exist after write`);
                        throw new Error(`File write verification failed: ${filePath}`);
                    }
                }

                // If we successfully created new files, delete old ones
                if (existingPath && existingPath !== baseFileName) {
                    await deleteOldImageFiles(existingPath);
                }

                return baseFileName;

            } catch (error) {
                console.error(`Error processing ${prefix} image:`, error);
                console.warn(`Keeping existing ${prefix} image due to processing error.`);
                return existingPath;
            }
        };

        // Get and validate file uploads with proper type checking
        const wideLogoEntry = formData.get('wideLogo');
        const squareLogoEntry = formData.get('squareLogo');

        // Convert to File or null using type guard
        const wideLogo = isFile(wideLogoEntry) ? wideLogoEntry : null;
        const squareLogo = isFile(squareLogoEntry) ? squareLogoEntry : null;


        // Process both logo types
        const [wideLogoPath, squareLogoPath] = await Promise.all([
            processImage(wideLogo, existingDomain.WideLogo, 'wide'),
            processImage(squareLogo, existingDomain.SquareLogo, 'square')
        ]);

        // Prepare update data
        const updateData = {
            Name: getString('Name', existingDomain.Name),
            Prefix: getString('Prefix', existingDomain.Prefix),
            ShowUrlName: getString('ShowUrlName', existingDomain.ShowUrlName),
            ChannelId: getString('ChannelId', existingDomain.ChannelId),
            CookieMinutes: getNumber('CookieMinutes', existingDomain.CookieMinutes),
            StyleIdDm: getBigInt('StyleIdDm', existingDomain.StyleIdDm),
            StyleIdLm: getBigInt('StyleIdLm', existingDomain.StyleIdLm),
            HeadTagScript: getString('HeadTagScript', existingDomain.HeadTagScript),
            HeadTagScriptLandingPage: getString('HeadTagScriptLandingPage', existingDomain.HeadTagScriptLandingPage),
            HeadTagScriptSearchPage: getString('HeadTagScriptSearchPage', existingDomain.HeadTagScriptSearchPage),
            GId: getString('GId', existingDomain.GId),
            AWId: getString('AWId', existingDomain.AWId),
            SendTo: getString('SendTo', existingDomain.SendTo),
            WideLogo: wideLogoPath,
            SquareLogo: squareLogoPath,
            ContactEmail: getString('ContactEmail', existingDomain.ContactEmail),
            Address: getString('Address', existingDomain.Address),
            AdsTxtContent: getString('AdsTxtContent', existingDomain.AdsTxtContent),
            UpdatedAt: new Date()
        };

        // Update domain in database
        const updatedDomain = await prisma.domain.update({
            where: { Id: id },
            data: updateData
        });

        return NextResponse.json({
            success: true,
            message: 'Domain updated successfully',
        });

    } catch (error) {
        console.error('Error processing request:', error);

        // More specific error handling
        if (error instanceof Error) {
            // Handle Prisma-specific errors
            if (error.message.includes('Unique constraint')) {
                return NextResponse.json(
                    { error: 'A domain with this name already exists' },
                    { status: 409 }
                );
            }

            // Handle foreign key constraint errors
            if (error.message.includes('Foreign key constraint')) {
                return NextResponse.json(
                    { error: 'Invalid reference data provided' },
                    { status: 400 }
                );
            }
        }

        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}