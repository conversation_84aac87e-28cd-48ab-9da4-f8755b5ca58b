import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { writeFile, access, constants } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { mkdirSync, existsSync } from 'fs';
import sharp from 'sharp';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || '/var/www/images/categories';
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MIN_FILE_SIZE = 1024; // 1KB minimum file size

type SharpImageFormat = keyof sharp.FormatEnum;

export async function PUT(req: NextRequest) {
    const user = await verifyToken(req);

    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        // Ensure upload directory exists and is writable
        if (!existsSync(UPLOAD_DIR)) {
            mkdirSync(UPLOAD_DIR, { recursive: true });
        }
        try {
            await access(UPLOAD_DIR, constants.W_OK);
        } catch (err) {
            console.error('Upload directory is not writable:', UPLOAD_DIR);
            return NextResponse.json(
                { error: 'Server configuration error: upload directory not writable' },
                { status: 500 }
            );
        }

        const formData = await req.formData();

        // Get fields from form data
        const Id = formData.get('id')?.toString();
        const Name = formData.get('name')?.toString();
        const Title = formData.get('title')?.toString();
        const ShowUrlName = formData.get('showUrlName')?.toString();
        const ShortDescription = formData.get('shortDescription')?.toString();
        const imageFile = formData.get('image') as File | null;

        if (!Id) {
            return NextResponse.json(
                { error: "Missing required fields: Id is required." },
                { status: 400 }
            );
        }

        // Get existing category
        const existingCategory = await prisma.category.findFirst({
            where: {
                Id,
                OR: [
                    { IsDeleted: false },
                    { IsDeleted: null }
                ]
            }
        });

        if (!existingCategory) {
            return NextResponse.json(
                { error: "Category not found" },
                { status: 404 }
            );
        }

        // Helper function to validate image formats
        function isSharpImageFormat(format: string): format is SharpImageFormat {
            const sharpFormats: SharpImageFormat[] = ['jpeg', 'png', 'webp', 'gif', 'tiff', 'raw'];
            return sharpFormats.includes(format as SharpImageFormat);
        }

        // Process image upload if new image is provided
        let imagePath: string | undefined = undefined;

        if (imageFile && imageFile.size > 0) {
            try {

                const buffer = Buffer.from(await imageFile.arrayBuffer());
                let mimeType = imageFile.type;
                let originalExtension: SharpImageFormat = 'jpeg'; // default fallback

                // Handle cases where browser doesn't detect MIME type
                if (mimeType === 'application/octet-stream') {
                    try {
                        const metadata = await sharp(buffer).metadata();
                        if (metadata.format && isSharpImageFormat(metadata.format)) {
                            mimeType = `image/${metadata.format}`;
                            originalExtension = metadata.format;
                        }
                    } catch (sharpError) {
                        throw new Error('Invalid image format');
                    }
                } else {
                    const ext = mimeType.split('/')[1];
                    if (ext && isSharpImageFormat(ext)) {
                        originalExtension = ext;
                    }
                }

                // Validate file type
                if (!ALLOWED_IMAGE_TYPES.includes(mimeType)) {
                    throw new Error(`Invalid file type: ${mimeType}`);
                }

                // Validate file size
                if (imageFile.size < MIN_FILE_SIZE) {
                    throw new Error(`File too small: ${imageFile.size} bytes`);
                }

                const uniqueId = uuidv4();
                const baseFileName = `category_${uniqueId}.${originalExtension}`;

                // Process and save images
                const [originalBuffer, thumbnailBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, 300, { fit: 'cover', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                await Promise.all([
                    writeFile(path.join(UPLOAD_DIR, baseFileName), originalBuffer),
                    writeFile(path.join(UPLOAD_DIR, `thumb_${uniqueId}.${originalExtension}`), thumbnailBuffer)
                ]);

                imagePath = baseFileName;
            } catch (error) {
                console.error('Error processing category image:', error);
                return NextResponse.json(
                    { error: 'Failed to process image. Please upload a valid image file.' },
                    { status: 400 }
                );
            }
        }

        // Generate ShowUrlName if Name is changed
        const showUrlName = Name && Name !== existingCategory.Name
            ? Name.replace(/[^a-zA-Z0-9]+/g, '-').toLowerCase()
            : existingCategory.ShowUrlName;

        // Prepare update data - only include fields that are provided
        const updateData: Record<string, any> = {};

        if (Name !== undefined) updateData.Name = Name;
        if (Title !== undefined) updateData.Title = Title;
        if (ShortDescription !== undefined) updateData.ShortDescription = ShortDescription;
        if (ShowUrlName !== undefined) updateData.ShowUrlName = ShowUrlName;
        if (imagePath !== undefined) updateData.Image = imagePath;

        // Perform the update within a transaction
        const [updatedCategory, verification] = await prisma.$transaction([
            prisma.category.update({
                where: { Id },
                data: updateData,
                select: {
                    Id: true,
                    Name: true,
                    Title: true,
                    Image: true,
                    ShortDescription: true,
                    ShowUrlName: true,
                },
            }),
            // Immediately verify the update
            prisma.category.findUnique({
                where: { Id },
            })
        ]);



        return NextResponse.json(
            {
                success: true,
                message: 'Category updated successfully',
                data: updatedCategory
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error updating category:", error instanceof Error ? error.message : String(error));
        return NextResponse.json(
            { error: "Failed to update category" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}