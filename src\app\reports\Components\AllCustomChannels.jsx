"use client";
import React, { useCallback, useRef, useState } from "react";
import axios from "axios";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import Swal from "sweetalert2";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";

const AllCustomChannels = () => {
  const [tableKey, setTableKey] = useState(0);
  const [showLoader, setShowLoader] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState("DisplayName");
  const [data, setData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const dataTableRef = useRef();

  const columns = [
    { id: "DisplayName", label: "Channel Name" },
    { id: "Name", label: "Name" },
    { id: "CustomChannelId", label: "Channel ID" },
  ];

  const fetchChannels = useCallback(async () => {
    try {
      setShowLoader(true);
      const response = await axios.get("/api/Channals/Get", {
        params: {
          q: searchTerm,
          page: page + 1,
          length: rowsPerPage,
          orderBy: orderBy,
          orderDir: order,
        },
        withCredentials: true,
      });
      setData(response.data.data || []);
      setTotalCount(response.data.pagination?.recordsFiltered || 0);
    } catch (error) {
      console.error("Error fetching channels:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load channels",
        timer: 3000,
        showConfirmButton: false,
      });
      setData([]);
      setTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [searchTerm, page, rowsPerPage, order, orderBy]);

  const handleRefreshData = async () => {
    try {
      setShowLoader(true);
      const syncResponse = await axios.get("/api/Channals/GetCustomChannals", {
        params: {
          customChannels: "true",
        },
        withCredentials: true,
      });

      await fetchChannels();

      await Swal.fire({
        icon: "success",
        title: "Success",
        text: syncResponse.data.message || "Channels synced successfully",
        timer: 3000,
        showConfirmButton: false,
      });
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text:
          error?.response?.data?.error || "Failed to refresh custom channels",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleChangePage = (newPage) => {
    setPage(newPage);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setPage(0);
  };

  React.useEffect(() => {
    fetchChannels();
  }, [fetchChannels]);

  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white">
              All Custom Channels
            </h1>
          </div>
        </div>

        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-xl border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6">
          <div className="flex mb-3 flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-center">
              <div className="w-full sm:w-64">
                <InputGroup
                  label="Search"
                  placeholder="Search channels..."
                  value={searchTerm}
                  handleChange={handleSearchChange}
                  type="text"
                  className="w-full"
                />
              </div>
              <div className="w-full mt-4 sm:w-64">
                <Button
                  type="button"
                  label="Get Custom Channels"
                  className="flex w-full items-center justify-center gap-2 sm:w-64"
                  variant="primary"
                  shape="rounded"
                  onClick={handleRefreshData}
                />
              </div>
            </div>
          </div>
          <CustomDataTable
            isLoading={showLoader}
            key={tableKey}
            ref={dataTableRef}
            columns={columns}
            rows={data}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handleChangePage}
            onRowsPerPageChange={setRowsPerPage}
            totalCount={totalCount}
            order={order}
            notShowAction={true}
            orderBy={orderBy}
            onRequestSort={handleRequestSort}
          />
        </div>
      </div>
    </>
  );
};

export default AllCustomChannels;