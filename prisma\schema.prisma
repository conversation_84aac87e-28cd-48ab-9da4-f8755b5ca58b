generator client {
  provider      = "prisma-client-js"
  output        = "./generated/client"
  binaryTargets = ["native", "debian-openssl-3.0.x", "linux-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AdminUser {
  Id                    String                  @id(map: "adminuser_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  Name                  String?
  Email                 String?                 @unique(map: "adminuser_email_key")
  Password              String?
  User_Type             String?
  Block                 Boolean?                @default(false)
  Number                String?
  Status                Boolean?                @default(true)
  CreatedAt             DateTime?               @default(now()) @db.Timestamp(6)
  UpdatedAt             DateTime?               @db.Timestamp(6)
  DisplayName           String?
  ProfilePic            String?
  AccessExpiration      DateTime?               @db.Timestamp(6)
  IsDeleted             Boolean?                @default(false)
  AboutMe               String?
  ArticleDetails        ArticleDetails[]
  StyleIdUserMappings   StyleIdUserMappings[]
  SubDomainUserMappings SubDomainUserMappings[]
}

model AdminUserSetting {
  Id                   String    @id(map: "adminusersetting_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  LmStyleId            String?
  DmStyleId            String?
  ChannalId            String?   @db.Uuid
  PubId                String?
  AdsAccountId         String?
  AdsClientId          String?
  HeadTagJSON          String?
  CreatedAt            DateTime? @default(now()) @db.Timestamp(6)
  UpdatedAt            DateTime? @db.Timestamp(6)
  CountAdsClick        String?
  CampaignClientId     String?
  CampaignClientSecret String?
  CampaignAccessToken  String?
  CampaignRefreshToken String?
  RevenueClientId      String?
  RevenueClientSecret  String?
  RevenueAccessToken   String?
  RevenueRefreshToken  String?
  Channals             Channals? @relation(fields: [ChannalId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_channals")
}

model Ads_AccountDetails {
  SNo             Int         @id(map: "account_details_pkey") @default(autoincrement())
  AccountId       BigInt      @unique(map: "AccountId")
  ResourceName    String?
  ClientCustomer  String?
  Level           Int?
  TimeZone        String?
  Manager         Boolean?
  DescriptiveName String?
  CurrencyCode    String?     @db.Char(10)
  Campaigns       Campaigns[]
}

model Ads_CampaignCountryDetails {
  SNo         Int       @id(map: "ads_campaigncountrydetails_pkey") @default(autoincrement())
  CampaignId  BigInt?
  CountryId   BigInt?
  Clicks      Int?
  Impressions BigInt?
  AverageCPC  Decimal?  @db.Decimal(18, 4)
  CostMicros  Decimal?  @db.Decimal(18, 4)
  Conversions Decimal?  @db.Decimal(18, 4)
  SegmentDate DateTime? @db.Date
}

model Ads_CampaignDetails {
  SNo                                                             Int       @id(map: "campaign_details_pkey") @default(autoincrement())
  CampaignId                                                      BigInt?
  ActiveViewCPM                                                   Decimal?  @db.Decimal(18, 4)
  ActiveViewCTR                                                   Decimal?  @db.Decimal(18, 4)
  ActiveViewImpressions                                           BigInt?
  ActiveViewMeasurability                                         Decimal?  @db.Decimal(18, 4)
  ActiveViewMeasurableCostMicros                                  BigInt?
  ActiveViewMeasurableImpressions                                 BigInt?
  ActiveViewViewAbility                                           Decimal?  @db.Decimal(18, 4)
  AllConversionsValue                                             Decimal?  @db.Decimal(18, 4)
  AllConversionsValueByConversionDate                             Decimal?  @db.Decimal(18, 4)
  AllConversions                                                  Decimal?  @db.Decimal(18, 4)
  AllConversionsByConversionDate                                  Decimal?  @db.Decimal(18, 4)
  AverageCost                                                     Decimal?  @db.Decimal(18, 4)
  AverageCPC                                                      Decimal?  @db.Decimal(18, 4)
  AverageCPM                                                      Decimal?  @db.Decimal(18, 4)
  Clicks                                                          Int?
  ConversionsFromInteractionsRate                                 Decimal?  @db.Decimal(18, 4)
  ConversionsValue                                                Decimal?  @db.Decimal(18, 4)
  ConversionsValueByConversionDate                                Decimal?  @db.Decimal(18, 4)
  Conversions                                                     Decimal?  @db.Decimal(18, 4)
  ConversionsByConversionDate                                     Decimal?  @db.Decimal(18, 4)
  CostMicros                                                      BigInt?
  CostPerAllConversions                                           Decimal?  @db.Decimal(18, 4)
  CostPerConversion                                               Decimal?  @db.Decimal(18, 4)
  CostPerCurrentModelAttributedConversion                         Decimal?  @db.Decimal(18, 4)
  CTR                                                             Decimal?  @db.Decimal(18, 4)
  CurrentModelAttributedConversions                               Decimal?  @db.Decimal(18, 4)
  CurrentModelAttributedConversionsFromInteractionsRate           Decimal?  @db.Decimal(18, 4)
  CurrentModelAttributedConversionsFromInteractionsValuePerIntera Decimal?  @db.Decimal(18, 4)
  CurrentModelAttributedConversionsValue                          Decimal?  @db.Decimal(18, 4)
  CurrentModelAttributedConversionsValuePerCost                   Decimal?  @db.Decimal(18, 4)
  Engagements                                                     BigInt?
  Impressions                                                     BigInt?
  InteractionRate                                                 Decimal?  @db.Decimal(18, 4)
  Interactions                                                    BigInt?
  InvalidClickRate                                                Decimal?  @db.Decimal(18, 4)
  InvalidClicks                                                   BigInt?
  GeneralInvalidClickRate                                         Decimal?  @db.Decimal(18, 4)
  GeneralInvalidClicks                                            BigInt?
  AverageTargetCpaMicros                                          BigInt?
  ValuePerAllConversions                                          Decimal?  @db.Decimal(18, 4)
  ValuePerAllConversionsByConversionDate                          Decimal?  @db.Decimal(18, 4)
  ValuePerConversion                                              Decimal?  @db.Decimal(18, 4)
  ValuePerConversionsByConversionDate                             Decimal?  @db.Decimal(18, 4)
  ValuePerCurrentModelAttributedConversion                        Decimal?  @db.Decimal(18, 4)
  SegmentDate                                                     DateTime? @db.Date

  @@unique([CampaignId, SegmentDate], map: "unique_campaigndetails_id_date")
}

model Ads_Campaigns {
  SNo                     Int                       @id(map: "campaigns_pkey") @default(autoincrement())
  AccountId               BigInt?
  CampaignId              BigInt?
  CustomerResourceName    String?
  CampaignResourceName    String?
  Status                  String?
  AdvertisingChannelType  String?
  NetworkSettings         Json?                     @db.Json
  ExperimentType          String?
  ServingStatus           String?
  BiddingStrategyType     String?
  GeoTargetTypeSetting    Json?                     @db.Json
  PaymentMode             String?
  Name                    String?
  StartDate               DateTime?                 @db.Date
  EndDate                 DateTime?                 @db.Date
  OptimizationScore       Decimal?                  @db.Decimal(18, 4)
  PrimaryStatus           String?
  PrimaryStatusReasons    String[]
  BrandGuidelinesEnabled  Boolean?
  SegmentDate             DateTime?                 @db.Date
  ArticleCampaignMappings ArticleCampaignMappings[]
}

model Ads_CampaignsBudget {
  SNo                        Int       @id(map: "ads_campaignsbudget_pkey") @default(autoincrement())
  CampaignId                 BigInt?
  CampaignBudgetId           BigInt?
  CampaignBudgetResourceName String?
  Name                       String?
  Status                     String?
  AmountMicros               BigInt?
  DeliveryMethod             String?
  ExplicitlyShared           Boolean?
  HasRecommendedBudget       Boolean?
  Period                     String?
  ReferenceCount             Int?
  Type                       String?
  SegmentDate                DateTime? @db.Date
}

model Ads_CountryMaster {
  SNo         Int     @id(map: "ads_countrymaster_pkey") @default(autoincrement())
  CountryId   BigInt?
  Name        String?
  CountryCode String?
}

model ArticleCampaignMappings {
  Id             String          @id(map: "campaignmappings_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  ArticleId      String?         @db.Uuid
  CreatedAt      DateTime?       @default(now()) @db.Timestamp(6)
  CampaignId     Int?
  ArticleDetails ArticleDetails? @relation(fields: [ArticleId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_Article")
  Ads_Campaigns  Ads_Campaigns?  @relation(fields: [CampaignId], references: [SNo], onDelete: NoAction, onUpdate: NoAction, map: "fk_ads_campaigns")
}

model ArticleDetails {
  Id                                            String                    @id(map: "articledetails_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  Title                                         String?
  Category                                      String?                   @db.Uuid
  Url                                           String?
  Description                                   String?
  MetaTitle                                     String?
  MetaDescription                               String?
  MetaKeys                                      String?
  StyleIdLm                                     String?                   @db.Uuid
  User_Id_Settings                              String?                   @db.Uuid
  AdRelatedSearches                             String?
  Remark                                        String?
  Image                                         String?
  Published                                     Boolean?                  @default(true)
  ShowArticle                                   Boolean?                  @default(true)
  ShowUrlName                                   String?
  CreatedAt                                     DateTime?                 @default(now()) @db.Timestamp(6)
  UpdatedAt                                     DateTime?                 @db.Timestamp(6)
  ShowsAds                                      Boolean?                  @default(true)
  CustomChannal                                 String?                   @db.Uuid
  Domain                                        String?                   @db.Uuid
  SubDomain                                     String?                   @db.Uuid
  ShortDescription                              String?
  IsDeleted                                     Boolean?                  @default(false)
  ReadTime                                      Int?
  CheckRelatedSearches2                         Boolean?
  AdReletadSearches2                            String?
  StyleIdDm                                     String?                   @db.Uuid
  ArticleCampaignMappings                       ArticleCampaignMappings[]
  AdminUser                                     AdminUser?                @relation(fields: [User_Id_Settings], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_adminuser")
  Category_ArticleDetails_CategoryToCategory    Category?                 @relation("ArticleDetails_CategoryToCategory", fields: [Category], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_category")
  Channals                                      Channals?                 @relation(fields: [CustomChannal], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_channals")
  Domain_ArticleDetails_DomainToDomain          Domain?                   @relation("ArticleDetails_DomainToDomain", fields: [Domain], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_domain")
  StyleIds_ArticleDetails_StyleIdLmToStyleIds   StyleIds?                 @relation("ArticleDetails_StyleIdLmToStyleIds", fields: [StyleIdLm], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_styleids")
  StyleIds_ArticleDetails_StyleIdDmToStyleIds   StyleIds?                 @relation("ArticleDetails_StyleIdDmToStyleIds", fields: [StyleIdDm], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_styleidsdm")
  SubDomain_ArticleDetails_SubDomainToSubDomain SubDomain?                @relation("ArticleDetails_SubDomainToSubDomain", fields: [SubDomain], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_subdomain")
}

model CampaignBudgets {
  SNo          Int       @id @default(autoincrement())
  CampaignId   BigInt
  BudgetId     BigInt?
  AmountMicros BigInt?
  SegmentDate  DateTime  @db.Date
  Campaigns    Campaigns @relation(fields: [CampaignId], references: [CampaignId], onDelete: NoAction, onUpdate: NoAction)

  @@unique([CampaignId, SegmentDate], map: "unique_campaign_budget_date")
}

model Campaigns {
  SNo                Int                 @id @default(autoincrement())
  CampaignId         BigInt              @unique
  AccountId          BigInt?
  Name               String?
  Status             String?
  Type               String?
  SegmentDate        DateTime?           @default(dbgenerated("CURRENT_DATE")) @db.Date
  CampaignBudgets    CampaignBudgets[]
  Ads_AccountDetails Ads_AccountDetails? @relation(fields: [AccountId], references: [AccountId], onDelete: NoAction, onUpdate: NoAction)
}

model Category {
  Id                                               String                     @id(map: "category_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  Name                                             String
  ShowUrlName                                      String?
  CreatedAt                                        DateTime?                  @default(now()) @db.Timestamp(6)
  UpdatedAt                                        DateTime?                  @db.Timestamp(6)
  IsDeleted                                        Boolean?                   @default(false)
  Title                                            String?
  Image                                            String?
  ShortDescription                                 String?
  ArticleDetails_ArticleDetails_CategoryToCategory ArticleDetails[]           @relation("ArticleDetails_CategoryToCategory")
  SubDomainCategoryMappings                        SubDomainCategoryMapping[]
}

model Channals {
  Id                   String             @id(map: "assignchannal_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  Name                 String?            @unique(map: "uq_channals_name")
  ReportingDimensionId String?            @unique(map: "uq_reportingdimensionid")
  DisplayName          String?
  Active               Boolean?           @default(true)
  CreatedAt            DateTime?          @default(now()) @db.Timestamp(6)
  UpdatedAt            DateTime?          @db.Timestamp(6)
  AdminUserSetting     AdminUserSetting[]
  ArticleDetails       ArticleDetails[]
  Revenue              Revenue[]
}

model Domain {
  Id                                           String           @id(map: "domain_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  Name                                         String?
  ShowUrlName                                  String?
  SubDomains                                   String[]
  Articles                                     String[]
  CreatedBy                                    String?          @db.Uuid
  CreatedAt                                    DateTime?        @default(now()) @db.Timestamp(6)
  UpdatedAt                                    DateTime?        @db.Timestamp(6)
  V                                            Int?
  Prefix                                       String?
  ChannelId                                    String?
  CookieMinutes                                Int?
  StyleIdDm                                    BigInt?
  StyleIdLm                                    BigInt?
  IsDeleted                                    Boolean?         @default(false)
  HeadTagScript                                String?
  HeadTagScriptLandingPage                     String?
  HeadTagScriptSearchPage                      String?
  GId                                          String?
  AWId                                         String?
  SendTo                                       String?
  WideLogo                                     String?
  SquareLogo                                   String?
  ContactEmail                                 String?
  Address                                      String?
  AdsTxtContent                                String?
  ArticleDetails_ArticleDetails_DomainToDomain ArticleDetails[] @relation("ArticleDetails_DomainToDomain")
  SubDomain_SubDomain_DomainToDomain           SubDomain[]      @relation("SubDomain_DomainToDomain")
}

model IpAddress {
  Id            String  @id @db.Uuid
  IpAddress     String? @unique(map: "Ip")
  NoOfBlackList Int?
}

model QueryAnalytics {
  Id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  BrowserId       String?
  IpAddress       String?
  URL             String?
  Keyword         String?
  Count           Int?
  AdsClickCounter Int?
  Country         String?
  State           String?
  Created_At      DateTime? @default(now()) @db.Timestamp(6)
  Domain          String?
  City            String?
}

model Revenue {
  Id                String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  Date              DateTime? @db.Date
  StyleId           String?
  ChannalId         String?
  Country           String?
  PlatfromType      String?
  EstimatedEarnings Decimal?  @db.Decimal(18, 4)
  Impressions       Decimal?  @db.Decimal(18, 4)
  ImpressionsRpm    Decimal?  @db.Decimal(18, 4)
  Clicks            Decimal?  @db.Decimal(18, 4)
  ImpressionsCtr    Decimal?  @db.Decimal(18, 4)
  CostPerClick      Decimal?  @db.Decimal(18, 4)
  Channals          Channals? @relation(fields: [ChannalId], references: [ReportingDimensionId], onDelete: NoAction, onUpdate: NoAction, map: "fk_ReportingDimensionId")

  @@unique([Date, StyleId, ChannalId, Country, PlatfromType], map: "Date_StyleId_ChannalId_Country_PlatfromType")
}

model SavedReport {
  Id        String    @id(map: "savedreport_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  Name      String?
  Title     String?
  CreatedAt DateTime? @default(now()) @db.Timestamp(6)
  UpdatedAt DateTime? @default(now()) @db.Timestamp(6)
}

model StyleIdUserMappings {
  Id        String     @id(map: "styleidmappings_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  UserId    String?    @db.Uuid
  CreatedAt DateTime?  @default(now()) @db.Timestamp(6)
  StyleId   String?    @db.Uuid
  V         Int?
  StyleIds  StyleIds?  @relation(fields: [StyleId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_StyleIds")
  AdminUser AdminUser? @relation(fields: [UserId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_adminuser")
}

model StyleIds {
  Id                                                String                @id(map: "styleids_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  StyleId                                           String?               @unique(map: "uq_styleid")
  Name                                              String?
  Prefix                                            String?
  CreatedBy                                         String?               @db.Uuid
  CreatedAt                                         DateTime?             @default(now()) @db.Timestamp(6)
  UpdatedAt                                         DateTime?             @db.Timestamp(6)
  V                                                 Int?
  DomainId                                          String?
  IsDeleted                                         Boolean?              @default(false)
  ArticleDetails_ArticleDetails_StyleIdLmToStyleIds ArticleDetails[]      @relation("ArticleDetails_StyleIdLmToStyleIds")
  ArticleDetails_ArticleDetails_StyleIdDmToStyleIds ArticleDetails[]      @relation("ArticleDetails_StyleIdDmToStyleIds")
  StyleIdUserMappings                               StyleIdUserMappings[]
}

model SubDomain {
  Id                                                 String                     @id(map: "subdomain_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  Name                                               String?
  Url                                                String?
  Domain                                             String?                    @db.Uuid
  HeadTag                                            String?
  CId                                                String?
  Articles                                           String[]
  CreatedBy                                          String?                    @db.Uuid
  CreatedAt                                          DateTime?                  @default(now()) @db.Timestamp(6)
  UpdatedAt                                          DateTime?                  @db.Timestamp(6)
  V                                                  Int?
  AccountId                                          String[]
  IsDeleted                                          Boolean?                   @default(false)
  HeadTagScript                                      String?
  HeadTagScriptLandingPage                           String?
  HeadTagScriptSearchPage                            String?
  GId                                                String?
  AWId                                               String?
  SendTo                                             String?
  WideLogo                                           String?
  SquareLogo                                         String?
  ArticleDetails_ArticleDetails_SubDomainToSubDomain ArticleDetails[]           @relation("ArticleDetails_SubDomainToSubDomain")
  Domain_SubDomain_DomainToDomain                    Domain?                    @relation("SubDomain_DomainToDomain", fields: [Domain], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_Domain")
  SubDomainUserMappings                              SubDomainUserMappings[]
  SubDomainCategoryMappings                          SubDomainCategoryMapping[]
}

model SubDomainUserMappings {
  Id          String     @id(map: "subdomainusermappings_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  SubDomainId String?    @db.Uuid
  UserId      String?    @db.Uuid
  CreatedAt   DateTime?  @default(now()) @db.Timestamp(6)
  V           Int?
  SubDomain   SubDomain? @relation(fields: [SubDomainId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_SubDomain")
  AdminUser   AdminUser? @relation(fields: [UserId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_adminuser")
}

model SubDomainCategoryMapping {
  Id          String     @id(map: "subdomaincategorymapping_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  SubDomainId String?    @db.Uuid
  CategoryId  String?    @db.Uuid
  CreatedAt   DateTime?  @default(now()) @db.Timestamp(6)
  CreatedBy   String?    @db.Uuid
  UpdatedAt   DateTime?  @db.Timestamp(6)
  IsDeleted   Boolean?   @default(false)
  SubDomain   SubDomain? @relation(fields: [SubDomainId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_SubDomainCategoryMapping_SubDomain")
  Category    Category?  @relation(fields: [CategoryId], references: [Id], onDelete: NoAction, onUpdate: NoAction, map: "fk_SubDomainCategoryMapping_Category")

  @@unique([SubDomainId, CategoryId], map: "unique_subdomain_category")
}

model adsanalytics {
  id           BigInt    @id @default(autoincrement())
  uuid         String    @unique @db.Uuid
  browser_id   String
  ip_address   String?
  state        String?
  country      String?
  url          String?
  keyword      String?
  is_ads_click Boolean?
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
}

model cookie_data {
  id           Int       @id @default(autoincrement())
  cookie_value String
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
}

model getGeoTargetConstants {
  SNo               Int                 @id @default(autoincrement())
  CountryId         BigInt              @unique(map: "unique_country_id")
  Name              String
  getGeographicView getGeographicView[]
}

model getGeographicView {
  SNo                   Int                   @id @default(autoincrement())
  CampaignId            BigInt
  CountryId             BigInt
  Clicks                Int?
  Impressions           BigInt?
  AverageCPC            Decimal?              @db.Decimal
  CostMicros            Decimal?              @db.Decimal
  Conversions           Decimal?              @db.Decimal
  SegmentDate           DateTime?             @db.Date
  getGeoTargetConstants getGeoTargetConstants @relation(fields: [CountryId], references: [CountryId], onDelete: NoAction, onUpdate: NoAction, map: "fk_country")

  @@index([CampaignId, CountryId, SegmentDate], map: "idx_geographic_view_campaign_country_date")
  @@index([SegmentDate], map: "idx_geographic_view_segment_date")
}

model timeinformation {
  uuid  String   @id @db.Uuid
  time1 DateTime @db.Timestamptz(6)
  time2 DateTime @db.Timestamptz(6)
}

model UserSetting {
  Id                   String  @id(map: "usersetting_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  CampaignClientId     String?
  CampaignClientSecret String?
  CampaignAccessToken  String?
  CampaignRefreshToken String?
  RevenueClientId      String?
  RevenueClientSecret  String?
  RevenueAccessToken   String?
  RevenueRefreshToken  String?
  PubId                String?
  AdsAccountId         String?
  AdsClientId          String?
}

model ArticleIdMap {
  SourceId      String    @id
  DestinationId String    @db.Uuid
  SourceType    String
  CreatedAt     DateTime? @default(now()) @db.Timestamp(6)
}
