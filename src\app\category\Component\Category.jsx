"use client";
import React, { useCallback, useEffect, useState } from "react";
import axios from "axios";
import Swal from "sweetalert2";
import useDebounce from "@/hooks/useDebounce";
import { useSearchParams } from "next/navigation";
import { IoCloseOutline } from "react-icons/io5";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import { FaPlus } from "react-icons/fa";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import Checkbox from "@/components/FormElements/checkbox";

const Category = () => {
  const searchParams = useSearchParams();
  const [showLoader, setShowLoader] = useState(false);
  const [categories, setCategories] = useState([]);
  const [domainList, setDomainList] = useState([]);
  const [domainListLoader, setDomainListLoader] = useState(false);
  const [selectedDomain, setSelectedDomain] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    title: "",
    shortDescription: "",
    showUrlName: "",
    image: null,
  });
  const [errors, setErrors] = useState({});
  const [modalState, setModalState] = useState({
    open: false,
    mode: "add",
    id: null,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("Name");
  const [isEditFormInitialized, setIsEditFormInitialized] = useState(false);
  const [editUrlMode, setEditUrlMode] = useState(false);
  const [isUrlManuallyModified, setIsUrlManuallyModified] = useState(false);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const columns = [
    {
      id: "Name",
      label: "Category Name",
    },
    {
      id: "ShowUrlName",
      label: "URL Name",
    },
    {
      id: "Title",
      label: "Title",
    },
  ];

  // Fetch domains for dropdown
  useEffect(() => {
    const fetchDomainList = async () => {
      try {
        setDomainListLoader(true);
        const response = await axios.get("/api/Domain/GetDropDown", {
          withCredentials: true,
        });
        if (response.data.success) {
          setDomainList(response.data.data);

          // Pre-select domain from URL parameters
          const domainIdFromUrl = searchParams.get('domainId');
          if (domainIdFromUrl && response.data.data.some(d => d.Id === domainIdFromUrl)) {
            setSelectedDomain(domainIdFromUrl);
          }
        }
      } catch (error) {
        console.error("Error fetching domains:", error);
      } finally {
        setDomainListLoader(false);
      }
    };

    fetchDomainList();
  }, [searchParams]);

  const fetchCategories = useCallback(async () => {
    if (!selectedDomain) {
      setCategories([]);
      setTotalCount(0);
      return;
    }

    try {
      setShowLoader(true);
      const response = await axios.get("/api/category/getAllCategory", {
        params: {
          page: page + 1,
          length: rowsPerPage,
          q: debouncedSearchTerm,
          orderBy,
          orderDir: order,
          domainId: selectedDomain,
          _: new Date().getTime(),
        },
        withCredentials: true,
      });

      if (response.status === 200) {
        setCategories(response.data.data);
        setTotalCount(
          response.data.pagination?.recordsFiltered ||
            response.data.data?.length ||
            0,
        );
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch categories",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  }, [selectedDomain, page, rowsPerPage, debouncedSearchTerm, orderBy, order]);

  // Generate slug function
  const generateSlug = (text) => {
    return text
      .replace(/[^\p{L}\p{N}\s-]/gu, '')
      .trim()
      .replace(/\s+/g, '-')
      .toLowerCase();
  };

  // Handle field changes
  const handleChange = (e) => {
    const { name, value, files } = e.target;
    
    if (name === "image") {
      setFormData(prev => ({ ...prev, image: files?.[0] || null }));
      return;
    }
    
    const updates = { [name]: value };
    
    // Auto-generate URL when:
    // - Editing name field AND
    // - (In add mode OR URL not manually modified) AND
    // - Not in edit URL mode
    if (name === "name" && 
        (modalState.mode === "add" || !isUrlManuallyModified) &&
        !(modalState.mode === "edit" && editUrlMode)) {
      updates.showUrlName = generateSlug(value);
    }
    
    // Track manual URL edits
    if (name === "showUrlName") {
      setIsUrlManuallyModified(true);
    }
    
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // Auto-generate showUrlName based on name for both add and edit modes
  useEffect(() => {
    if (
      !formData.name ||
      isUrlManuallyModified ||
      (modalState.mode === "edit" && !isEditFormInitialized) ||
      (modalState.mode === "edit" && editUrlMode)
    ) {
      return;
    }

    const generatedSlug = generateSlug(formData.name);
    setFormData((prev) => ({ 
      ...prev, 
      showUrlName: generatedSlug 
    }));
  }, [
    formData.name, 
    isUrlManuallyModified, 
    modalState.mode, 
    isEditFormInitialized,
    editUrlMode
  ]);

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = "Category name is required";
    if (!formData.showUrlName.trim()) {
      newErrors.showUrlName = "URL name is required";
    } else if (!/^[a-z0-9-]+$/.test(formData.showUrlName)) {
      newErrors.showUrlName = "URL can only contain lowercase letters, numbers and hyphens";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleOpenAddModal = () => {
    setFormData({
      name: "",
      title: "",
      shortDescription: "",
      showUrlName: "",
      image: null,
    });
    setErrors({});
    setIsUrlManuallyModified(false);
    setEditUrlMode(true);
    setModalState({
      open: true,
      mode: "add",
      id: null,
    });
  };

  const handleOpenEditModal = async (rowData) => {
    try {
      Swal.fire({
        title: "Loading Category Data",
        html: "Please wait while we fetch the category details...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });
  
      const response = await axios.get(
        `/api/category/GetById?id=${rowData.Id}`,
        {
          withCredentials: true,
        },
      );
  
      const categoryData = response.data.data[0];
      
      setFormData({
        id: categoryData.Id,
        name: categoryData.Name,
        title: categoryData.Title || "",
        shortDescription: categoryData.ShortDescription || "",
        showUrlName: categoryData.ShowUrlName || generateSlug(categoryData.Name),
        image: null,
      });
      
      setEditUrlMode(false);
      setIsUrlManuallyModified(!!categoryData.ShowUrlName);
      setErrors({});
      setIsEditFormInitialized(true);
  
      Swal.close();
  
      setModalState({
        open: true,
        mode: "edit",
        id: rowData.Id,
      });
    } catch (error) {
      console.error("Error fetching category data:", error);
      Swal.close();
      await Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          "Failed to load category data. Please try again.",
        icon: "error",
        confirmButtonText: "OK",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  const handleCloseModal = () => {
    setModalState({
      open: false,
      mode: "add",
      id: null,
    });
    setErrors({});
    setIsUrlManuallyModified(false);
    setEditUrlMode(false);
    setIsEditFormInitialized(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      setModalState((prev) => ({ ...prev, open: true }));
      return;
    }

    try {
      setShowLoader(true);

      const data = new FormData();
      data.append("name", formData.name.trim());
      data.append("title", formData.title.trim());
      data.append("shortDescription", formData.shortDescription.trim() || "");
      data.append("showUrlName", formData.showUrlName.trim() || "");
      data.append("domainId", selectedDomain);
      if (formData.image) {
        data.append("image", formData.image);
      }

      if (modalState.mode === "add") {
        const response = await axios.post("/api/category/addCategory", data, {
          withCredentials: true,
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Success",
            text: "Category added successfully",
            timer: 2000,
            showConfirmButton: false,
          });
        }
      } else {
        data.append("id", modalState.id);
        const response = await axios.put(`/api/category/EditCategory`, data, {
          withCredentials: true,
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Updated",
            text: "Category updated successfully",
            timer: 2000,
            showConfirmButton: false,
          });
        }
      }

      setFormData({
        name: "",
        title: "",
        shortDescription: "",
        showUrlName: "",
        image: null,
      });
      setErrors({});
      setIsUrlManuallyModified(false);
      setIsEditFormInitialized(false);
      handleCloseModal();
      fetchCategories();
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text:
          error?.response?.data?.error ||
          (modalState.mode === "add"
            ? "Failed to add category"
            : "Failed to update category"),
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const handleDeleteCategory = async (rowData) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        setShowLoader(true);
        const response = await axios.delete(`/api/category/DeleteCategory`, {
          data: { Id: rowData.Id },
          withCredentials: true,
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Category has been deleted.",
            timer: 2000,
            showConfirmButton: false,
          });
          fetchCategories();
        }
      } catch (error) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to delete category",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setShowLoader(false);
      }
    }
  };

  const handleViewCategory = (rowData) => {
    window.open(
      `${process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE}/category/${rowData?.ShowUrlName?.toLowerCase()}`,
      "_blank",
    );
  };

  const handleDomainChange = (item) => {
    setSelectedDomain(item?.Id ? item?.Id : "");
    setPage(0);
  };

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl text-white">
              Category Management by Domain
            </h1>
            {selectedDomain && (
              <p className="text-sm text-blue-100 mt-2">
                Managing categories for: {domainList.find(d => d.Id === selectedDomain)?.Name || 'Selected Domain'}
              </p>
            )}
          </div>
        </div>
        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-xl border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6">
          <div className="mb-5 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
              {selectedDomain && (
                <>
                  <div className="mt-4 w-full sm:w-64">
                    <Button
                      type="button"
                      label="Add Category"
                      variant="primary"
                      shape="rounded"
                      icon={<FaPlus size={14} />}
                      className="ml-auto flex w-full items-center justify-center gap-2 sm:w-64"
                      onClick={handleOpenAddModal}
                    />
                  </div>
                  <div className="w-full md:w-64">
                    <InputGroup
                      placeholder="Search..."
                      label="Search"
                      value={searchTerm}
                      handleChange={(e) => {
                        setSearchTerm(e.target.value);
                        setPage(0);
                      }}
                      type="text"
                      className="w-full"
                    />
                  </div>
                </>
              )}
            </div>
            {domainListLoader ? (
              <div className="flex justify-center py-8 sm:w-64">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              </div>
            ) : (
              <div className="w-full sm:w-64">
                <SearchableDropdown
                  label="Select Domain"
                  options={domainList}
                  placeholder="Select a Domain"
                  value={selectedDomain}
                  onChange={handleDomainChange}
                  displayKey="Name"
                  idKey="Id"
                />
              </div>
            )}
          </div>
          {selectedDomain && (
            <div className="mb-8">
              <CustomDataTable
                isLoading={showLoader}
                columns={columns}
                rows={categories}
                page={page}
                rowsPerPage={rowsPerPage}
                onPageChange={setPage}
                onRowsPerPageChange={setRowsPerPage}
                totalCount={totalCount}
                order={order}
                orderBy={orderBy}
                onRequestSort={(event, property) => {
                  const isAsc = orderBy === property && order === "asc";
                  setOrder(isAsc ? "desc" : "asc");
                  setOrderBy(property);
                }}
                onView={handleViewCategory}
                onEdit={handleOpenEditModal}
                onDelete={handleDeleteCategory}
              />
            </div>
          )}
        </div>
        <Dialog
          open={modalState.open}
          onClose={handleCloseModal}
          fullWidth
          maxWidth="sm"
          PaperProps={{
            sx: {
              maxHeight: "90vh",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              color: "white",
              py: 2,
              px: 3,
            }}
            className="bg-primary text-white"
          >
            <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
              {modalState.mode === "add" ? "Add New" : "Edit"} Category
            </span>
            <IconButton
              aria-label="close"
              onClick={handleCloseModal}
              sx={{
                color: "white",
              }}
            >
              <IoCloseOutline size={24} />
            </IconButton>
          </DialogTitle>

          <DialogContent dividers sx={{ py: 3, px: 3 }}>
            <form style={{ display: "flex", flexDirection: "column", gap: 16 }}>
              <InputGroup
                label="Category Name"
                type="text"
                name="name"
                value={formData.name}
                handleChange={handleChange}
                placeholder="Enter category name"
                required
                error={errors.name}
              />
              <div className="flex items-start gap-4">
                <div className="flex-1">
                  <InputGroup
                    label="URL Name"
                    type="text"
                    name="showUrlName"
                    value={formData.showUrlName}
                    handleChange={handleChange}
                    placeholder="Enter URL name (e.g., category-name)"
                    required
                    error={errors.showUrlName}
                    readOnly={modalState.mode === "edit" && !editUrlMode}
                    disabled={modalState.mode === "edit" && !editUrlMode}
                  />
                </div>

                {modalState.mode === "edit" && (
                  <div className="mt-6">
                    <Checkbox
                      label="Edit URL"
                      checked={editUrlMode}
                      withIcon="check"
                      withBg
                      radius="default"
                      onChange={(e) => {
                        const shouldEdit = e.target.checked;
                        setEditUrlMode(shouldEdit);

                        // When unchecking, regenerate from name
                        if (!shouldEdit) {
                          setFormData(prev => ({
                            ...prev,
                            showUrlName: generateSlug(prev.name)
                          }));
                          setIsUrlManuallyModified(false);
                        }
                      }}
                    />
                  </div>
                )}
              </div>


              <InputGroup
                label="Title"
                type="text"
                name="title"
                value={formData.title}
                handleChange={handleChange}
                placeholder="Enter category title"
              />
              <InputGroup
                label="Short Description"
                type="text"
                name="shortDescription"
                value={formData.shortDescription}
                handleChange={handleChange}
                placeholder="Enter short description"
              />
              <InputGroup
                label="Image"
                type="file"
                name="image"
                accept="image/jpeg,image/png,image/webp,image/gif"
                fileStyleVariant="style1"
                handleChange={handleChange}
                placeholder="Upload category image"
              />
            </form>
          </DialogContent>

          <DialogActions sx={{ py: 2, px: 3 }}>
            <Button
              type="submit"
              label={
                showLoader
                  ? "Processing..."
                  : modalState.mode === "add"
                    ? "Add Category"
                    : "Update Category"
              }
              variant="primary"
              shape="rounded"
              onClick={handleSubmit}
              disabled={showLoader}
            />
          </DialogActions>
        </Dialog>
      </div>
    </>
  );
};

export default Category;