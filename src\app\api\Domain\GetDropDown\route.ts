import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            User_Type: string;
            Id: string;
        };

        const user = await verifyToken(req) as AuthenticatedUser;

        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        let domainWhere: any = {
            IsDeleted: false
        };

        if (user.User_Type !== 'Super Admin' && user.User_Type !== 'Admin') {
            domainWhere = {
                ...domainWhere,
                SubDomain_SubDomain_DomainToDomain: {
                    some: {
                        IsDeleted: false,
                        SubDomainUserMappings: {
                            some: {
                                UserId: user.Id
                            }
                        }
                    }
                }
            };
        }

        const domains = await prisma.domain.findMany({
            where: domainWhere,
            select: {
                Id: true,
                Name: true,
                ShowUrlName: true
            },
            orderBy: {
                CreatedAt: 'desc'
            }
        });

        return NextResponse.json({
            success: true,
            data: domains
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}